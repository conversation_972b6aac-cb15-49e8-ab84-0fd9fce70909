<?php

namespace App\Exports;

    use Illuminate\Contracts\View\View;
    use Maatwebsite\Excel\Concerns\FromView;
    use Maatwebsite\Excel\Concerns\WithEvents;
    use Maatwebsite\Excel\Events\AfterSheet;
    use PhpOffice\PhpSpreadsheet\RichText\RichText;
    use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
    use PhpOffice\PhpSpreadsheet\Style\Alignment;
    use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

    class AdvanceChallanreport implements FromView, WithEvents
    {
        protected $branches;
        protected $report;
        protected $selectedBranchId;
        protected $heads;
        protected $report_name;
        protected $branch;
        protected $params;

        public function __construct($branches, $report, $selectedBranchId, $selectedBranchName, $heads, $report_name, $params)
        {
            $this->branches = $branches;
            $this->report = $report;
            $this->selectedBranchId = $selectedBranchId;
            $this->heads = $heads;
            $this->report_name = $report_name;
            $this->params = $params;
            $this->branch = $selectedBranchName;
        }

        /**
         * Export the employees data to an Excel view.
         */
        public function view(): View
        {
            $is_branch = true;
            // Pass only the table-related data to the export view
            return view('studentReports.exports.Advance_Challan_report', [
                'branchName' => $this->branch,
                'branches' => $this->branches,
                'report' => $this->report,
                'selectedBranchId' => $this->selectedBranchId,
                'heads' => $this->heads,
                'report_name' => $this->report_name,
                'branch' => $this->branch,
                'params' => $this->params,
                'is_branch' => $is_branch,
            ]);
        }

        public function registerEvents(): array
        {
            return [
                AfterSheet::class => function (AfterSheet $event) {
                    $sheet = $event->sheet->getDelegate();

                    // Page setup: Fit to one page, Landscape, A4
                    $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
                    $sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
                    $sheet->getPageSetup()->setFitToPage(true);
                    $sheet->getPageSetup()->setFitToWidth(1);
                    $sheet->getPageSetup()->setFitToHeight(0); // unlimited height

                    // 🔁 Repeat heading row (row 5)
                    $sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(7, 7);
                    $sheet = $event->sheet->getDelegate();
                    $sheet->setShowGridlines(false);
                    // Optional: Margins
                    $sheet->getPageMargins()->setTop(0.5);
                    $sheet->getPageMargins()->setBottom(0.5);
                    $sheet->getPageMargins()->setLeft(0.5);
                    $sheet->getPageMargins()->setRight(0.5);
                    // $sheet->getHeaderFooter()->setOddFooter('&LGenerated on &D &T&RPage &P of &N');

                    // Logo insertion
                    $highestColumn = $sheet->getHighestColumn();
                    $colIndex = Coordinate::columnIndexFromString($highestColumn); // Convert to number
                    $colIndex--; // Move one column to the left
                    $highestColumn = Coordinate::stringFromColumnIndex($colIndex); // Convert back to letter
                    $originalPath = public_path('assets/images/lynx2.jpg');

                    if (file_exists($originalPath) && function_exists('imagecreatefromjpeg')) {
                        $img = imagecreatefromjpeg($originalPath);
                        imagefilter($img, IMG_FILTER_GRAYSCALE);
                        $tmpPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'logo_gray.png';
                        imagepng($img, $tmpPath);
                        imagedestroy($img);
                    } else {
                        $tmpPath = $originalPath;
                    }

                    $drawing = new Drawing();
                    $drawing->setName('Logo');
                    $drawing->setDescription('School Logo (grayscale)');
                    $drawing->setPath($tmpPath);
                    $drawing->setHeight(75);
                    $drawing->setOffsetX(10);
                    $drawing->setOffsetY(10);
                    $drawing->setCoordinates($highestColumn . '1');
                    $drawing->setWorksheet($sheet);

                    $lastDataRow = $sheet->getHighestRow();
                    $sigLineRow = $lastDataRow + 2; // underscores
                    $sigTextRow = $lastDataRow + 3; // labels
                    $highestIndex = Coordinate::columnIndexFromString($highestColumn); // e.g. 8
                    $insetIndex = max(1, $highestIndex - 1);                       // at least 1
                    $insetColumn = Coordinate::stringFromColumnIndex($insetIndex);
                    $pageCountRow = $lastDataRow + 4;
                    $generatedDate = date('d-M-Y');
                    // Merge the entire row (e.g., row 25)
                    $highestColumnLetter = $sheet->getHighestColumn();
                    $mergedRange = "A{$sigLineRow}:{$highestColumnLetter}{$sigLineRow}";
                    $sheet->mergeCells($mergedRange);

                    // Build signature line text with left and right alignment
                    $signatureLine = new RichText();
                    $signatureLine->createText('________________________');

                    // Add enough space in between to push second line to right side
                    $colCount = Coordinate::columnIndexFromString($highestColumnLetter);
                    $space = str_repeat(' ', $colCount * 3); // Adjust spacing depending on column width
                    $signatureLine->createText($space);

                    $signatureLine->createText('________________________');

                    // Set into merged cell
                    $sheet->setCellValue("A{$sigLineRow}", $signatureLine);
                    $sheet->getStyle("A{$sigLineRow}")->getFont()->setBold(true);
                    $sheet->getStyle("A{$sigLineRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_DISTRIBUTED);

                    // for heading row
                    $highestColumnLetter = $sheet->getHighestColumn();
                    $sheet->getStyle('A1')->applyFromArray([
                        'font' => [
                            'bold' => true,
                            'size' => 28,
                            'name' => 'Edwardian Script ITC', // Will only work if the font is installed on the system
                        ],
                    ]);
                    // Apply style to entire Heading Row
                    $sheet->getStyle("A8:{$highestColumnLetter}9")->applyFromArray([
                        'font' => [
                            'bold' => true,
                            'size' => 8,
                            'name' => 'calibri',
                        ],
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                            'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,

                        ],
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                'color' => ['argb' => 'FF000000'], // Black
                            ],
                        ],
                        'fill' => [
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'startColor' => [
                                'argb' => 'FFBFBFBF', // Light gray
                            ],
                        ],
                    ]);

                    $sheet->getColumnDimension('A')->setWidth(5);
                    $sheet->getColumnDimension('B')->setWidth(5);
                    $sheet->getColumnDimension('E')->setWidth(20);

                    // style col font size 8px and align center
                    $sheet->getStyle("A10:D{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    $sheet->getStyle("E10:G{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                    $sheet->getStyle("E10:E{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT)->setWrapText(true);
                    $sheet->getStyle("H10:H{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    $sheet->getStyle("I10:I{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                    $sheet->getStyle("J10:J{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                    $sheet->getStyle("K10:{$highestColumnLetter}{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                    $sheet->getStyle("K10:{$highestColumnLetter}{$lastDataRow}")->getNumberFormat()->setFormatCode('#,##0');
                    $sheet->getStyle("{$highestColumnLetter}10:{$highestColumnLetter}{$lastDataRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    $sheet->getStyle("A10:{$highestColumnLetter}{$lastDataRow}")->getFont()->setSize(8);
                },
            ];
        }
    }
