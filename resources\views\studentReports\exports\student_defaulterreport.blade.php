@include('student.exports.header')
<table>
    <thead>
        <tr>
            <td colspan="{{ (count($monthsArray)) + 9 }}" style="text-align: center;">
            </td>
        </tr>
        @php
            $i = 1;
            $yearMonthCounts = [];
            foreach ($monthsArray as $monthYear) {
                [$month, $year] = explode('-', $monthYear);
                if (!isset($yearMonthCounts[$year])) {
                    $yearMonthCounts[$year] = 0;
                }
                $yearMonthCounts[$year]++;
                $yearMonths[] = $month;
            }
            $grandMonthlyTotals = array_fill(0, count($monthsArray), 0);
            $grandTotal = 0;
            $grandMonthlyFeeTotal = 0;
            $grandArrearsTotal = 0;
        @endphp

        <tr style="background:gray; text-align:center; font-size: 8px; border: 3px solid black; border-collapse: collapse;">
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 30px;">Sr.</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 40px;">B Sr No.</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 40px;">Roll No</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 120px;">Student Name</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 60px;">Reg type</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 70px;">Class</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 75px;">Phone No</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 60px;">Monthly Fee</th>
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 60px; text-align:center;">Arrears</th>
            @foreach($yearMonthCounts as $ak => $year)
            <th colspan="{{ $year }}" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 100px; text-align:center;">{{ $ak }}</th>
            @endforeach
            <th rowspan="2" style="background: gray; text-align:center; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 70px;">Total</th>
        </tr>
        <tr style="background:gray; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse;">
            @foreach($yearMonths as $month)
            <th style="background: gray; font-size: 8px; font-weight: bold; border: 3px solid black; border-collapse: collapse; width: 45px; text-align:center;">{{ date('M', mktime(0, 0, 0, (int)$month, 1)) }}</th>
            @endforeach
        </tr>
    </thead>
    <tbody>
        @foreach($reportData as $data)
            @if($data['challans']->count() < 1)
                @continue
            @endif
            <tr style="background: gray">
                <td colspan="{{ count($monthsArray) + 10 }}" style="font-size: 8px; font-weight: 700; border: 2px solid black; border-collapse: collapse; background: gray;">{{ $data['branch'] }}</td>
            </tr>
            @php
                $branchMonthlyTotals = array_fill(0, count($monthsArray), 0);
                $branchTotal = 0;
                $branchMonthlyFeeTotal = 0;
                $branchArrearsTotal = 0;
                $brsr = 1;
            @endphp
            @foreach($data['challans'] as $index => $challan)
                @foreach($challan as $chall)
                    @php
                        $studentTotal = 0;
                        $monthlyFee = @$chall->monthly_fee ?? 0;
                        $arrears = 0; // You can calculate actual arrears if needed
                    @endphp
                    <tr>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{{ $i++ }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{{ $brsr++ }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{{ @$chall->student->roll_no }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:left;">{{ @$chall->student->stdname }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{{ @$chall->student->registeroption->name }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:left;">{{ @$chall->class->name }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{!! str_replace(',', '<br>', @$chall->student->fatherphone) !!}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{{ $monthlyFee }}</td>
                        <td style="font-size: 8px; border-collapse: collapse; text-align:center;">{{ $arrears }}</td>
                        @foreach ($monthsArray as $l => $monthYear)
                            @php
                                [$month, $year] = explode('-', $monthYear);
                                $formattedDate = date('Y-m-01', strtotime("$year-$month-01"));
                                $specificdata = collect($challan)->firstWhere('fee_month', $formattedDate);
                                $price = $specificdata ? $specificdata->total_amount - ($specificdata->paid_amount + $specificdata->concession_amount) : 0;
                                $studentTotal += $price;
                                $branchMonthlyTotals[$l] += $price;
                                $grandMonthlyTotals[$l] += $price;
                            @endphp
                            <td style="font-size: 8px; border-collapse: collapse; text-align: right;">{{ $price }}</td>
                        @endforeach
                        <td style="font-size: 8px; border-collapse: collapse; text-align: right;">{{ $studentTotal }}</td>
                    </tr>
                    @php
                        $branchTotal += $studentTotal;
                        $branchMonthlyFeeTotal += $monthlyFee;
                        $branchArrearsTotal += $arrears;
                        $grandMonthlyFeeTotal += $monthlyFee;
                        $grandArrearsTotal += $arrears;
                    @endphp
                    @break
                @endforeach
            @endforeach
            <tr style="background: #dcdcdc; font-weight: bold;">
                <td colspan="7" style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-collapse: collapse; font-weight: bold; text-align:center;">Branch Total:</td>
                <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-collapse: collapse; text-align:center; font-weight: bold;">{{ $branchMonthlyFeeTotal }}</td>
                <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-collapse: collapse; text-align:center; font-weight: bold;">{{ $branchArrearsTotal }}</td>
                @foreach ($branchMonthlyTotals as $monthlyTotal)
                    <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-collapse: collapse; text-align:right; font-weight: bold;">{{ $monthlyTotal }}</td>
                @endforeach
                <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-collapse: collapse; text-align:right; font-weight: bold;">{{ $branchTotal }}</td>
            </tr>
            @php
                $grandTotal += $branchTotal;
            @endphp
        @endforeach
        <tr>
            <td colspan="{{ count($monthsArray) + 9 }}" style="background: #fff; height: 10px; border: none;"></td>
        </tr>
        <tr>
            <td colspan="{{ count($monthsArray) + 9 }}" style="background: #fff; height: 10px; border: none;"></td>
        </tr>
        <tr>
            <td colspan="{{ count($monthsArray) + 9 }}" style="background: #fff; height: 10px; border: none;"></td>
        </tr>
        <tr style="background: #cccccc; font-weight: bold;">
            <td colspan="7" style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; border-collapse: collapse; font-weight: bold; text-align:center;">Grand Total:</td>
            <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; border-collapse: collapse; text-align:center; font-weight: bold;">{{ $grandMonthlyFeeTotal }}</td>
            <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; border-collapse: collapse; text-align:center; font-weight: bold;">{{ $grandArrearsTotal }}</td>
            @foreach ($grandMonthlyTotals as $grandMonthlyTotal)
                <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; border-collapse: collapse; text-align:right; font-weight: bold;">{{ $grandMonthlyTotal }}</td>
            @endforeach
            <td style="font-size: 8px; background: #dcdcdc; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; border-collapse: collapse; text-align:right; font-weight: bold;">{{ $grandTotal }}</td>
        </tr>
    </tbody>
</table>
@include('student.exports.footer')
