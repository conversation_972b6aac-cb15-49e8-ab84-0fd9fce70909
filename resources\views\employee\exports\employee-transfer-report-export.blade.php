<table class="table">
    <thead class="table_heads">
        @include('student.exports.header')
        <tr>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Sr. No') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Reason of Transfer') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Transfer Date') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Employee No') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Designation') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Gross Salary') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Transfer IN Branch') }}</th>
            <th
                style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">
                {{ __('Transfer OUT Branch') }}</th>
        </tr>
    </thead>
    <tbody>
        @php $sr = 1; @endphp
        @foreach ($transfers as $transfer)
            @php
                // Initialize gross
                $gross = 0;

                // Fetch last payscale detail
                $lastPayscaleDetail = $transfer->employee->employee_payscale_details->last();

                if ($lastPayscaleDetail) {
                    $scale = \App\Models\EmployeeScale::with('employeeScaleHeads')->find(
                        $lastPayscaleDetail->pay_scale_id,
                    );

                    if ($scale) {
                        foreach ($scale->employeeScaleHeads as $head) {
                            $gross += $head->head_value ?? 0;
                        }

                        // Add other allowances
                        $gross +=
                            $lastPayscaleDetail->drns +
                            $lastPayscaleDetail->conv +
                            $lastPayscaleDetail->misc +
                            $lastPayscaleDetail->other_add;
                    }
                }
            @endphp
            <tr>
                {{-- Sr. No --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ $sr++ }}
                </td>

                {{-- Reason of Transfer --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ $transfer->transfer_reason ?? 'N/A' }}
                </td>

                {{-- Transfer Date --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ \Carbon\Carbon::parse($transfer->transfer_date)->format('d-m-Y') }}
                </td>

                {{-- Employee No --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ $transfer->employee->employee_id ?? 'N/A' }}
                </td>

                {{-- Designation --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ optional($transfer->employee->designation)->name ?? 'N/A' }}
                </td>

                {{-- Gross Salary --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ $gross > 0 ? number_format($gross) : 'N/A' }}
                </td>

                {{-- Transfer IN Branch --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ $transfer->branch_to->name ?? 'N/A' }}
                </td>

                {{-- Transfer OUT Branch --}}
                <td style="font-size: 8px; border: 1px solid black;">
                    {{ $transfer->branch_from->name ?? 'N/A' }}
                </td>
            </tr>
        @endforeach

        @include('student.exports.footer')
    </tbody>

    {{-- <tbody>
    </tbody> --}}
</table>
