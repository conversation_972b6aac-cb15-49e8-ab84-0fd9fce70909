@extends('layouts.admin')

@section('page-title')
    {{ __('Staff Child Report') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Staff Child Report') }}</li>
@endsection

@push('script-page')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.2/html2pdf.bundle.min.js"></script>
    <script>
        function branchemployees(id) {
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: "{{ route('branch.employees') }}",
                type: "POST",
                data: {
                    id: id
                },
                dataType: 'json',
                success: function(result) {
                    if (result.status == 'success') {
                        $('#employee_id').empty();
                        $('#employee_id').append($('<option>', {
                            value: '',
                            text: 'Select Employee'
                        }));

                        for (var j = 0; j < result.employee.length; j++) {
                            var cls = result.employee[j];
                            $('#employee_id').append($('<option>', {
                                value: cls.id,
                                text: cls.name
                            }));
                        }
                    }
                }
            });
        }
        // New printPdf function
        function printPdf() {
            const form = document.getElementById('staffChildReport');
            let url = new URL(form.action, window.location.origin);
            let params = new URLSearchParams(new FormData(form));
            params.set('print', 'pdf');
            url.search = params.toString();
            window.open(url.toString(), '_blank');
        }

        function submitWithPrintFlag(type = "pdf") {
            const form = $('#staffChildReport');
            if (type === "pdf")
                $('#is_print').val(1);
            if (type === "excel")
                $('#is_excel').val(1);
            form.attr('target', '_blank').submit().removeAttr('target');
            $('#is_print').val(0);
            $('#is_excel').val(0);
        }
    </script>
@endpush

@section('content')
    {{-- @if (\Auth::user()->type == 'company') --}}
        <div class="row">
            <div class="col-sm-12">
                <div class="mt-2" id="multiCollapseExample1">
                    <div class="card">
                        <div class="card-body filter_change">
                            {{ Form::open(['route' => ['staffChildReport'], 'method' => 'GET', 'id' => 'staffChildReport']) }}
                            <div class="row d-flex justify-content-end">
                                <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                    <div class="btn-box">
                                        {{ Form::label('branches', __('Branches'), ['class' => 'form-label']) }}
                                        {{ Form::select('branches', $branches, request()->input('branches'), ['class' => 'form-control select', 'onchange' => 'branchemployees(this.value)']) }}
                                    </div>
                                </div>
                                <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                    <div class="btn-box">
                                        {{ Form::label('employee_id', __('Employee'), ['class' => 'form-label']) }}
                                        {{ Form::select('employee_id', $employees, request()->input('employee_id'), ['class' => 'form-control select', 'required' => 'required', 'id' => 'employee_id', 'placeholder' => __('Select Employee')]) }}
                                    </div>
                                </div>
                                <input type="hidden" name="is_print" id="is_print" value="0">
                                <div class="col-auto float-end ms-2 mt-4">
                                    {{-- //print  --}}
                                    <a href="#" class="btn mx-1 btn-sm btn-outline-primary"
                                        onclick="printPdf(); return false;" 
                                        data-bs-title="{{ __('Print') }}">
                                        <span class="btn-inner--icon">Print</span>
                                    </a>
                                    <input type="text" class="d-none" id="is_excel" name="is_excel" value="0">
                                    <a type="button" class="btn mx-1 btn-sm btn-outline-primary me-2" data-bs-title="excel"
                                        onclick="submitWithPrintFlag('excel')">
                                        <span class="btn-inner--icon">Export</span>
                                    </a>
                                    {{-- // actions  --}}
                                    <a href="#" class="btn mx-1 btn-sm btn-outline-primary"
                                        onclick="document.getElementById('staffChildReport').submit(); return false;"
                                         data-bs-title="{{ __('Apply') }}">
                                        <span class="btn-inner--icon">Search</span>
                                    </a>
                                    <a href="{{ route('staffChildReport') }}" class="btn mx-1 btn-sm btn-outline-danger"
                                         data-bs-title="{{ __('Reset') }}">
                                        <span class="btn-inner--icon">Clear</span>
                                    </a>
                                    {{-- <a class="btn mx-1 btn-sm btn-outline-success" onclick="generatePDF()"><span
                                    class="btn-inner--icon">Print</span></a> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {{-- @endif --}}
    <div class="content" id="report-content">
        <div class="card p-4">
            <p style="font-family: Edwardian Script ITC; font-size: 3rem; text-align: center;"><b>The Lynx School</b></p>
            <p style="font-size: 1.1rem; text-align: center; margin-top:-20px"><b> </b></p>
            <p style="text-align:center; font-weight:900; font-size:1rem;">
                @isset($_GET['branches'])
                    {{ !empty(\Auth::user()->getBranch($_GET['branches'])) ? \Auth::user()->getBranch($_GET['branches'])->name : 'All Branches' }}
                @endisset
            </p>
            <div class="table-responsive mt-4">
                <table class="datatable">
                    <thead class="table_heads">
                        <tr>
                            <th>Sr#</th>
                            <th>Branch</th>
                            <th>Employee</th>
                            <th>Emp. Service Period</th>
                            <th>Designation</th>
                            <th>Child Roll No</th>
                            <th>Child Branch</th>
                            <th>Child Name</th>
                            <th>Child Class</th>
                            <th>D.O.A</th>
                            <th>Tuition Fee </th>
                            <th>Child Concession % </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($data as $key => $child)
                            <tr>
                                <td>{{ $key + 1 }}</td>
                                <td>{{ @$child->employee ? @$child->employee->userbranch->name : '' }}</td>
                                <td>{{ @$child->employee ? @$child->employee->name : '' }}</td>
                                <td>{{ @$child->employee ? @$child->employee->getEmployeeTenure(@$child->employee->id) : '' }}
                                </td>
                                <td>{{ @$child->employee ? @$child->employee->designation->name : '' }}</td>
                                <td>{{ @$child->student ? @$child->student->roll_no : '' }}</td>
                                <td>{{ @$child->student ? @$child->student->branches->name : '' }}</td>
                                <td>{{ @$child->student ? @$child->student->stdname : '' }}</td>
                                <td>{{ @$child->student ? @$child->student->class->name : '' }}</td>
                                <td>{{ @$child->enrollment ? @$child->enrollment->adm_date : '' }}</td>
                                @php
                                   

                                    // 1. Get Tuition Fee Head
                                    $tutionfeehead = \App\Models\FeeHead::where('fee_head', 'like', '%Tuition Fee%')->first();

                                    // 2. Get Student Fee Structure (safe access to student and fee head)
                                    $studentId = optional(optional($child)->student)->id;
                                    $headId = optional($tutionfeehead)->id;
                                    $stdfeestr = \App\Models\StudentFeeStructure::where('reg_id', $studentId)
                                        ->where('head_id', $headId)
                                        ->first();

                                    // 3. Get Concession for student
                                    $concession = \App\Models\Concession::with('student', 'class')
                                        ->where('student_id', $studentId)
                                        ->first();

                                    // Initialize concession policy and related values
                                    $concession_policy = null;
                                    $concession_heads = collect();
                                    $concession_amt = 0;
                                    $totalpercentage = 0;

                                    if ($concession && $concession->concession_id) {
                                        // 4. Fetch concession policy only if the concession exists
                                        $concession_policy = \App\Models\ConcessionPolicy::with([
                                            'concession',
                                            'concession.student',
                                            'concession.student.enrollment',
                                            'concession.class',
                                            'concession.student.session',
                                        ])->find($concession->concession_id);

                                        // 5. Get Concession Policy Heads
                                        if ($concession_policy) {
                                            $concession_heads = \App\Models\ConcessionPolicyHead::where(
                                                'concession_id',
                                                $concession_policy->id,
                                            )
                                                ->where('percentage', '!=', 0)
                                                ->get();
                                        }

                                        // 6. Calculate Total Percentage
                                        foreach ($concession_heads as $dta) {
                                            $totalpercentage += $dta->percentage;
                                        }
                                    }
                                @endphp

                                <td>{{ @$stdfeestr ? @$stdfeestr->amount : '' }}</td>
                                <td>{{ @$totalpercentage }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="12" class="text-center">No Data Available</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

    </div>
@endsection
