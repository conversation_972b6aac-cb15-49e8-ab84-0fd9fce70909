{{-- @dd($datas[0]->employee->employee_payscale_details[0]['paymode']) --}}


@foreach ($datas as $key => $data)
    @php
        $payscale = $data->employee->employee_payscale_details->last();
    @endphp
    <table>
        <tr>
            <td colspan="3"></td>
            <td colspan="11" style="font-size: 48px; font-family: 'Edwardian Script ITC';">The Lynx School</td>
        </tr>
        <tr>
            <td colspan="12"></td>
            <td colspan="2" style="font-weight: bold; font-size: 14px;">Employee Pay Slip</td>
        </tr>
        <tr>
            <td colspan="2"></td>
            <td colspan="6" style="font-weight: bold; font-size: 14px;">
                {!! \Auth::user()->getBranch($requestdata['branches'])
                    ? \Auth::user()->getBranch($requestdata['branches'])->name
                    : 'Main Branch' !!}
            </td>
            <td colspan="2"></td>
            <td colspan="3" style="font-weight: bold; font-size: 14px;">For the Month of June, 2025</td>
        </tr>

    @php
        // Employee Information
        $employeeInfo = [
            'emp_number' => '702',
            'name' => 'Mrs AQSA FATIMA',
            'title' => 'TEACHER',
            'department' => 'ACADEMIC',
            'payment_date' => '17-Jul-25',
            'mode' => 'Bank',
            'bank_branch' => 'N/A',
            'ntn' => 'N/A',
        ];

        // Leave Balances
        $leaveBalances = [
            'casual leave' => ['opening' => 10, 'taken' => 1, 'balance' => 0],
            'annual leave' => ['opening' => 0, 'taken' => 0, 'balance' => 0],
            'working days' => 30,
        ];

        // Earnings Structure
        $earnings = [
            'Gross Salary' => [
                ['label' => 'Initial Basic', 'pm' => 32435, 'ytd' => 97305],
                ['label' => 'Medical Allowance', 'pm' => 3244, 'ytd' => 9732],
                ['label' => 'House Rent', 'pm' => 12974, 'ytd' => 38922],
            ],
            'Enticements' => [
                ['label' => 'Conveyance Allowance', 'pm' => 500, 'ytd' => 1500],
                ['label' => 'Fuel Allowance', 'pm' => 1500, 'ytd' => 4500],
                ['label' => 'Mobile Allowance', 'pm' => 0, 'ytd' => 0],
                ['label' => 'Others', 'pm' => null, 'ytd' => null],
            ],
            'Adjustments' => [['label' => 'Stop Salary (May, 25)', 'pm' => 30140, 'ytd' => null]],
        ];

        // Deductions
        $deductions = [
            ['label' => 'Employee Security', 'pm' => 0, 'ytd' => 0],
            ['label' => 'E.O.B.I', 'pm' => 370, 'ytd' => 1110],
            ['label' => 'P.E.S.S.I', 'pm' => 0, 'ytd' => 0],
            ['label' => 'Income Tax', 'pm' => 0, 'ytd' => 0],
            ['label' => 'Other Deduction', 'pm' => 0, 'ytd' => 0],
            ['label' => 'Advance', 'pm' => 0, 'ytd' => 0],
            ['label' => 'Stop Salary', 'pm' => 0, 'ytd' => '-'],
            ['label' => 'Training Course', 'pm' => 0, 'ytd' => 0],
            ['label' => 'Others', 'pm' => 0, 'ytd' => 0],
            ['label' => 'Loan Emp Security', 'pm' => 0, 'ytd' => 0],
        ];

        // Employer Contributions
        $employerContributions = [
            'Employee Security Balance Y.T.D' => [['label' => 'Employee Security', 'amount' => '50000']],
            'Employer Contribution P.M' => [
                ['label' => 'Eobi Contribution', 'amount' => 1850],
                ['label' => 'Pessi Contribution', 'amount' => 2220],
                ['label' => 'Child Concession', 'amount' => 25000],
            ],
        ];

        // Calculate subtotals
        // $grossSalaryTotal = ['pm' => 0, 'ytd' => 0];
        // foreach ($earnings['Gross Salary'] as $item) {
        //     $grossSalaryTotal['pm'] += $item['pm'] ?? 0;
        //     $grossSalaryTotal['ytd'] += $item['ytd'] ?? 0;
        // }

        // $enticementsTotal = ['pm' => 0, 'ytd' => 0];
        // foreach ($earnings['Enticements'] as $item) {
        //     $enticementsTotal['pm'] += $item['pm'] ?? 0;
        //     $enticementsTotal['ytd'] += $item['ytd'] ?? 0;
        // }

        // $deductionsTotal = ['pm' => 0, 'ytd' => 0];
        // foreach ($deductions as $deduction) {
        //     $deductionsTotal['pm'] += $deduction['pm'];
        //     $deductionsTotal['ytd'] += $deduction['ytd'];
        // }

        // $netBalance = 50000;
        // $costToCompany = 61820;

    @endphp
        <!-- Employee Information Section -->
        <tr>
            <td></td>
            <td colspan="13" style="border: 1px solid black; border-bottom: 0px solid white;"></td>
        </tr>
        <tr style="border: 1px solid black;">
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td style="font-weight: bold; font-size: 9px;">Employee#</td>
            <td style="text-align: left; font-size: 9px;">{{ $data->employee->id }}</td>
            <td colspan="2"></td>
            <td style="font-weight: bold; font-size: 9px;">Leaves Balances</td>
            <td style="font-weight: bold; font-size: 9px; text-align: center;">C/L</td>
            <td style="font-weight: bold; font-size: 9px; text-align: center;">A/L</td>
            <td></td>
            <td style="font-weight: bold; font-size: 9px;">Payment Date</td>
            <td></td>
            <td style="font-size: 9px;">{{ $data->payment_date ?? '-' }}</td>
            <td style="border-right: 1px solid black;"></td>
        </tr>
        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td style="font-weight: bold; font-size: 9px;">Name</td>
            <td colspan="2" style="font-size:9px;">{{ $data->employee->name }}</td>
            <td></td>
            <td style="font-weight: bold; font-size: 9px;">O.Balance</td>
            <td style="text-align: center; font-size: 9px;">{{ $leaveBalances['casual leave']['opening'] ?? '0' }}</td>
            <td style="text-align: center; font-size: 9px;">{{ $leaveBalances['annual leave']['opening'] ?? '0' }}</td>
            <td></td>
            <td style="font-weight: bold; font-size: 9px;">Mode</td>
            <td></td>
            <td style="font-size:9px;">{{ $data->employee->employee_payscale_details->first()->paymode ?? '-' }}</td>
            <td style="border-right: 1px solid black;"></td>
        </tr>
        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td style="font-weight: bold; font-size: 9px;">Corporate Title</td>
            <td style="font-size:9px;">{{ $data->employee->designation->name }}</td>
            <td colspan="2"></td>
            <td style="font-weight: bold; font-size: 9px;">Leaves</td>
            <td style="text-align: center; font-size: 9px;">{{ $leaveBalances['casual leave']['taken'] ?? '0' }}</td>
            <td style="text-align: center; font-size: 9px;">{{ $leaveBalances['annual leave']['taken'] ?? '0' }}</td>
            <td></td>
            <td style="font-weight: bold; font-size: 9px;">Bank/Branch</td>
            <td></td>
            <td style="font-size:9px;"></td>
            <td style="border-right: 1px solid black;"></td>
        </tr>
        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td style="font-weight: bold; font-size: 9px;">Department</td>
            <td style="font-size:9px;">{{ $data->employee->department->name ?? '-' }}</td>
            <td colspan="2"></td>
            <td style="font-weight: bold; font-size: 9px;">C.Balance</td>
            <td style="text-align: center; font-size: 9px;">{{ $leaveBalances['casual leave']['balance'] ?? '0' }}</td>
            <td style="text-align: center;">{{ $leaveBalances['annual leave']['balance'] ?? '0' }}</td>
            <td></td>
            <td style="font-weight: bold; font-size: 9px;">N.T.N</td>
            <td></td>
            <td style="font-size:9px;">{{ $data->employee->employee_payscale_details->first()->account_number ?? '-' }}</td>
            <td style="border-right: 1px solid black;"></td>
        </tr>
        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td colspan="4"></td>
            <td style="font-weight: bold; font-size: 9px;">Working Days</td>
            <td style="text-align: center; font-size: 9px;">{{ $leaveBalances['working days'] ?? '0' }}</td>
            <td colspan="6" style="border-right: 1px solid black;"></td>
        </tr>

        <!-- Table Headers -->
        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td colspan="3" style="text-align: center; font-weight: bold; border: 1px solid black; font-size: 9px;">
                EARNINGS
            </td>
            <td></td>
            <td colspan="3" style="text-align: center; font-weight: bold; border: 1px solid black; font-size: 9px;">
                DEDUCTIONS</td>
            <td></td>
            <td colspan="3" style="text-align: center; font-weight: bold; border: 1px solid black; font-size: 9px;">
            </td>
            <td style="border-right: 1px solid black;"></td>
        </tr>

        {{-- <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">Gross
                        Salary</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        Y.T.D</td>
                    <td></td>
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">Heads
                    </td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        Y.T.D</td>
                    <td></td>
                    <td colspan="3"
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">
                        Employee Security Balance Y.T.D</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr> --}}

        @php

            $GrossRs = 0;
            $NetGrossRs = 0;
            $StopSalary = 30140;

            // ----------- Earnings (with group headings)
            $flatEarnings = [];
            foreach ($earnings as $category => $items) {
                $flatEarnings[] = [
                    'category' => $category,
                    'label' => $category == 'Adjustments' ? 'Net Gross Rs.' : $category,
                    'pm' => $category == 'Adjustments' ? $NetGrossRs + $GrossRs : 'P.M',
                    'ytd' => $category == 'Adjustments' ? '' : 'Y.T.D',
                    'head' => true,
                    'total' => false,
                ];

                foreach ($items as $item) {
                    if ($category == 'Gross Salary') {
                        $GrossRs += (int) $item['pm'];
                    }
                    if ($category == 'Enticements') {
                        $NetGrossRs += (int) $item['pm'];
                    }
                    $flatEarnings[] = [
                        'category' => $category,
                        'label' => $item['label'] ?? '',
                        'pm' => $item['pm'] ?? '',
                        'ytd' => $item['ytd'] ?? '',
                        'head' => false,
                        'total' => false,
                    ];
                }

                if ($category === 'Gross Salary') {
                    $flatEarnings[] = [
                        'category' => $category,
                        'label' => 'Gross Rs.',
                        'pm' => $GrossRs,
                        'ytd' => $GrossRs * 3,
                        'head' => false,
                        'total' => true,
                    ];
                    $flatEarnings[] = [
                        'category' => $category,
                        'label' => '',
                        'pm' => '',
                        'ytd' => '',
                        'head' => false,
                        'total' => false,
                    ];
                }
            }

            // ----------- Deductions (optional grouping — for now just one heading row)
            $flatDeductions = [];
            $deductionsTotalPM = 0;
            $deductionsTotalYTD = 0;

            if (count($deductions)) {
                $flatDeductions[] = [
                    'label' => 'Heads',
                    'pm' => 'P.M',
                    'ytd' => 'Y.T.D',
                    'head' => true,
                    'total' => false,
                ];

                foreach ($deductions as $item) {
                    $deductionsTotalPM += (int) $item['pm'];
                    $deductionsTotalYTD += (int) $item['ytd'];
                    $flatDeductions[] = [
                        'label' => $item['label'] ?? '',
                        'pm' => $item['pm'] ?? '',
                        'ytd' => $item['ytd'] ?? '',
                        'head' => false,
                        'total' => false,
                    ];
                }
            }

            // ----------- Employer Contributions (with heading)

            $flatEmployerContributions = [];
            $NetBalanceRs = 0;
            $CostToCompanyContributions = 0;

            foreach ($employerContributions as $category => $items) {
                $flatEmployerContributions[] = [
                    'category' => $category,
                    'label' => $category,
                    'amount' => '',
                    'head' => true,
                    'total' => false,
                ];

                foreach ($items as $item) {
                    if ($category == 'Employee Security Balance Y.T.D') {
                        $NetBalanceRs += (int) $item['amount'];
                    }
                    if ($category == 'Employer Contribution P.M') {
                        $CostToCompanyContributions += (int) $item['amount'];
                    }
                    $flatEmployerContributions[] = [
                        'category' => $category,
                        'label' => $item['label'],
                        'amount' => $item['amount'],
                        'head' => false,
                        'total' => false,
                    ];
                }

                if ($category === 'Employee Security Balance Y.T.D') {
                    $flatEmployerContributions[] = [
                        'category' => $category,
                        'label' => '',
                        'amount' => '',
                        'head' => false,
                        'total' => false,
                    ];
                    $flatEmployerContributions[] = [
                        'category' => $category,
                        'label' => 'Net Balance Rs.',
                        'amount' => $NetBalanceRs,
                        'head' => false,
                        'total' => true,
                    ];
                    $flatEmployerContributions[] = [
                        'category' => $category,
                        'label' => '',
                        'amount' => '',
                        'head' => false,
                        'total' => false,
                    ];
                }
            }

            // dd($flatEmployerContributions);

            // ----------- Equalize lengths
            $maxLength = max(count($flatEarnings), count($flatDeductions), count($flatEmployerContributions));

            while (count($flatEarnings) < $maxLength) {
                $flatEarnings[] = [
                    'category' => '',
                    'label' => '',
                    'pm' => '',
                    'ytd' => '',
                    'head' => false,
                    'total' => false,
                ];
            }

            while (count($flatDeductions) < $maxLength) {
                $flatDeductions[] = ['label' => '', 'pm' => '', 'ytd' => '', 'head' => false, 'total' => false];
            }

            while (count($flatEmployerContributions) < $maxLength) {
                $flatEmployerContributions[] = ['label' => '', 'amount' => '', 'head' => false, 'total' => false];
            }

            // ----------- Final result
            $result = [
                'earnings' => $flatEarnings,
                'deductions' => $flatDeductions,
                'employer_contributions' => $flatEmployerContributions,
            ];

            // dd($result);

        @endphp



        @for ($i = 0; $i < count($result['earnings']); $i++)
            <tr>
                {{-- Blank Cells for alignment --}}
                <td></td>
                <td style="border-left: 1px solid black;"></td>

                {{-- EARNINGS --}}
                @if ($result['earnings'][$i]['head'])
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px; text-transform: capitalize;">
                        {{ $result['earnings'][$i]['label'] }}
                    </td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px; text-transform: uppercase; {{ (float)$result['earnings'][$i]['pm'] != "0" ? "text-align: right;" : "text-align: center;" }}">
                        {{ (float)$result['earnings'][$i]['pm'] != "0" ? number_format((float)$result['earnings'][$i]['pm']) : $result['earnings'][$i]['pm'] }}
                    </td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px; text-transform: uppercase;">
                        {{ $result['earnings'][$i]['ytd'] }}
                    </td>
                @else
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;{{ $result['earnings'][$i]['total'] == true ? 'border: 1px solid black; font-weigth: bold;' : '' }}">
                        {{ $result['earnings'][$i]['label'] }}
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;{{ $result['earnings'][$i]['total'] == true ? 'border: 1px solid black; font-weigth: bold;' : '' }}">
                        {{ $result['earnings'][$i]['pm'] != "" ? number_format((float) $result['earnings'][$i]['pm']) : $result['earnings'][$i]['pm'] }}
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;{{ $result['earnings'][$i]['total'] == true ? 'border: 1px solid black; font-weigth: bold;' : '' }}">
                        {{ $result['earnings'][$i]['ytd'] != "" ? number_format((float) $result['earnings'][$i]['ytd']) : $result['earnings'][$i]['ytd'] }}
                    </td>
                @endif

                {{-- Spacer between columns --}}
                <td></td>

                {{-- DEDUCTIONS --}}
                @if ($result['deductions'][$i]['head'] == true)
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">
                        {{ $result['deductions'][$i]['label'] }}
                    </td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; text-align: center; font-weight: bold; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; text-align: center; font-weight: bold; font-size: 9px;">
                        Y.T.D</td>
                @else
                    {{-- <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold; border-top: 0px solid white; border-bottom: 0px solid white; {{ $result['employer_contributions'][$i]['total'] == true ? 'border: 1px solid black; font-weigth: bold;' : '' }}">
                        {{ $result['deductions'][$i]['label'] }}
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right; border-top: 0px solid white; border-bottom: 0px solid white; {{ $result['employer_contributions'][$i]['total'] == true ? 'border: 1px solid black; font-weigth: bold;' : '' }}">
                        {{ $result['deductions'][$i]['pm'] }}
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right; border-top: 0px solid white; border-bottom: 0px solid white; {{ $result['employer_contributions'][$i]['total'] == true ? 'border: 1px solid black; font-weigth: bold;' : '' }}">
                        {{ $result['deductions'][$i]['ytd'] }}
                    </td> --}}

                    <td
                        style="font-size: 9px; font-weight: bold; border-left: 1px solid black; border-right: 1px solid black;">
                        {{ $result['deductions'][$i]['label'] }}
                    </td>
                    <td
                        style="font-size: 9px; text-align: right; border-left: 1px solid black; border-right: 1px solid black;">
                        {{ $result['deductions'][$i]['pm'] != "" ? number_format((float) $result['deductions'][$i]['pm']) : $result['deductions'][$i]['pm'] }}
                    </td>
                    <td
                        style="font-size: 9px; text-align: right; border-left: 1px solid black; border-right: 1px solid black;">
                        {{ $result['deductions'][$i]['ytd'] != "" ? number_format((float) $result['deductions'][$i]['ytd']) : $result['deductions'][$i]['pm'] }}
                    </td>
                @endif

                {{-- Spacer between columns --}}
                <td></td>

                {{-- EMPLOYER CONTRIBUTIONS --}}
                @if ($result['employer_contributions'][$i]['head'])
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; border-right: 0px solid #d8d8d8; font-size: 9px;">
                        {{ $result['employer_contributions'][$i]['label'] }}
                    </td>
                    <td style="background: #d8d8d8; border-bottom: 1px solid black; border-top: 1px solid black;">
                    </td>
                    <td
                        style="background: #d8d8d8; text-align: right; border: 1px solid black; border-left: 0px solid #d8d8d8; font-weight: bold; font-size: 9px;">
                        {{ $result['employer_contributions'][$i]['amount'] }}
                    </td>
                @else
                    <td
                        style="border-left: 1px solid black; font-weight: bold; font-size: 9px; {{ $result['employer_contributions'][$i]['total'] == true ? 'border: 1px solid black; border-right: 1px solid white;' : '' }} ">
                        {{ $result['employer_contributions'][$i]['label'] }}
                    </td>
                    <td
                        style="{{ $result['employer_contributions'][$i]['total'] == true ? 'border-top: 1px solid black; border-bottom: 1px solid black;' : '' }}">
                    </td>
                    <td
                        style="text-align: right; border-right: 1px solid black; font-size: 9px; {{ $result['employer_contributions'][$i]['total'] == true ? 'border: 1px solid black; border-left: 1px solid white;' : '' }}">
                        {{ $result['employer_contributions'][$i]['amount'] != "" ? number_format((float) $result['employer_contributions'][$i]['amount']) : $result['employer_contributions'][$i]['amount'] }}
                    </td>
                @endif

                {{-- Final border --}}
                <td style="border-right: 1px solid black;"></td>
            </tr>
        @endfor

        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td style="border: 1px solid black; font-size: 9px; font-weight: bold;">Total Rs.</td>
            <td style="border: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">
                {{ number_format((float) $NetGrossRs + $StopSalary + $GrossRs) }}
            </td>
            <td style="border: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">6,000
            </td>
            <td></td>
            <td style="border: 1px solid black; font-size: 9px; font-weight: bold;">Total Rs.</td>
            <td style="border: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">
                {{ number_format((float) $deductionsTotalPM) }}
            </td>
            <td style="border: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">
                {{ number_format((float) $deductionsTotalYTD) }}
            </td>
            <td></td>
            <td style="border: 1px solid black; border-right: 0px solid white; font-size: 9px; font-weight: bold;">
                Cost to Company </td>
            <td style="border-bottom: 1px solid black; border-top: 1px solid black;"></td>
            <td
                style=" border: 1px solid black; border-left: 0px solid white; font-size: 9px; text-align: right; font-weight: bold;">
                {{ number_format((float) $CostToCompanyContributions + $GrossRs) }}
            </td>
            <td style="border-right: 1px solid black;"></td>
        </tr>
        {{-- <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">Gross
                        Salary</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        Y.T.D</td>
                    <td></td>
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">Heads
                    </td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        Y.T.D</td>
                    <td></td>
                    <td colspan="3"
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">
                        Employee Security Balance Y.T.D</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>

                <!-- Earnings -->
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Initial Basic</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        32,435</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        97,305</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Employee Security</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td style="border-left: 1px solid black; font-weight: bold; font-size: 9px;">Employee Security</td>
                    <td></td>
                    <td style="text-align: right; border-right: 1px solid black; font-size: 9px;">0</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Medical Allowance</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        3,244</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        9,732</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        E.O.B.I</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        370</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        1,110</td>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td></td>
                    <td style="border-right: 1px solid black;"></td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        House Rent</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        12,974</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        38,922</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        P.E.S.S.I</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td style="border-left: 1px solid black; border-bottom: 1px solid black;"></td>
                    <td style="border-bottom: 1px solid black;"></td>
                    <td style="border-right: 1px solid black; border-bottom: 1px solid black;"></td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <!-- Empty rows for earnings -->
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="border: 1px solid black; font-size: 9px; font-weight: bold;">Gross Rs.</td>
                    <td style="border: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">48,653
                    </td>
                    <td style="border: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">145,959
                    </td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Income Tax</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td
                        style="border: 1px solid black; border-right: 0px solid white; font-weight: bold; font-size: 9px;">
                        Net Balance Rs.</td>
                    <td style="border-bottom: 1px solid black;"></td>
                    <td
                        style="text-align: right; font-weight: bold; border: 1px solid black; border-left: 0px solid white; font-size: 9px;">
                        50,000</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="border: 1px solid black; font-size: 9px; font-weight: bold;"></td>
                    <td style="border: 1px solid black; font-size: 9px; font-weight: bold; text-align: center;"></td>
                    <td style="border: 1px solid black; font-size: 9px; font-weight: bold; text-align: center;">
                    </td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Other Deduction</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td colspan="3" style="border: 1px solid black; font-size: 9px; font-weight: bold;">
                    </td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="border: 1px solid black; font-size: 9px; background-color: #d8d8d8; font-weight: bold;">
                        Enticements </td>
                    <td
                        style="border: 1px solid black; font-size: 9px; background-color: #d8d8d8; font-weight: bold; text-align: center;">
                        P.M</td>
                    <td
                        style="border: 1px solid black; font-size: 9px; background-color: #d8d8d8; font-weight: bold; text-align: center;">
                        Y.T.D</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Advance</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td colspan="3"
                        style="border: 1px solid black; font-size: 9px; background-color: #d8d8d8; font-weight: bold;">
                        Employer Contribution P.M</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Conveyance Allowance </td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">500</td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">1500</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Stop Salary</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td style="border-left: 1px solid black; font-size: 9px; font-weight: bold;">Eobi Contribution
                    </td>
                    <td></td>
                    <td style=" border-right: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">
                        1850</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Fuel Allowance</td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">1500</td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">4500</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Training Course</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td style="border-left: 1px solid black; font-size: 9px; font-weight: bold;">Pessi Contribution
                    </td>
                    <td></td>
                    <td style=" border-right: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">
                        2,220</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Mobile Allowance</td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">0</td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">0</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Others</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td style="border-left: 1px solid black; font-size: 9px; font-weight: bold;">Child Concession </td>
                    <td></td>
                    <td style=" border-right: 1px solid black; font-size: 9px; text-align: right; font-weight: bold;">
                        25,000</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Others</td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;"></td>
                    <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;"></td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                        Loan Emp Security</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                        0</td>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td></td>
                    <td style="border-right: 1px solid black;"></td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="border: 1px solid black; font-size: 9px; font-weight: bold; background: #d8d8d8;">Net
                        Gross Rs.</td>
                    <td
                        style="border: 1px solid black; font-size: 9px; background: #d8d8d8; text-align: right; font-weight: bold;">
                        32,750
                    </td>
                    <td style="border: 1px solid black; font-size: 9px; background: #d8d8d8; text-align: right;"></td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                    </td>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td></td>
                    <td style="border-right: 1px solid black;"></td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="border: 1px solid black; font-size: 9px; font-weight: bold;">Stop Salary (May, 25)</td>
                    <td style="border: 1px solid black; font-size: 9px; text-align: right;">30,140</td>
                    <td style="border: 1px solid black; font-size: 9px; text-align: right;">-</td>
                    <td></td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                    </td>
                    <td
                        style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                    </td>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td></td>
                    <td style="border-right: 1px solid black;"></td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                --}}

        <tr>
            <td></td>
            <td colspan="13" style="border-left: 1px solid black; border-right: 1px solid black;"></td>
        </tr>
        <tr>
            <td></td>
            <td style="border-left: 1px solid black;"></td>
            <td style="font-weight: bold; text-align: right; font-size: 9px;" colspan="6">Total Amount
                Disbursed Rs.</td>
            <td style="font-weight: bold; text-align: right; border: 4px solid black; font-size: 9px;">
                {{ number_format((float) ($NetGrossRs + $StopSalary + $GrossRs - $deductionsTotalPM)) }}
            </td>
            <td colspan="5" style="border-right: 1px solid black;"></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="13" style="border: 1px solid black; border-top: 0px solid white;"></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="12" style="color: #808080;">This is a system generated document and does not require a
                signature</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="13" style="border-bottom: 1px dotted black;"></td>
        </tr>
    </table>
@endforeach





















{{-- Dynamic Code --}}
@php
    $deductions = [
        'Employee Security' => !empty($data->emp_sec) ? $data->emp_sec : '0',
        'E.O.B.I' => !empty($data->eobi) ? $data->eobi : '0',
        'P.E.S.S.I' => !empty($data->pessi) ? $data->pessi : '0',
        'Income Tax' => !empty($data->it) ? $data->it : '0',
        'Advance' => !empty($payscale->advance) ? $payscale->advance : '0',
        'Stop Salary' => !empty($data->stop_sal) ? $data->stop_sal : '0',
        'Training Course' => !empty($payscale->training) ? $payscale->training : '0',
        'Others' => !empty($payscale->other) ? $payscale->other : '0',
        'Loan Emp Security' => !empty($data->loan_emp_sec) ? $data->loan_emp_sec : '0',
        'Other Deduction' => !empty($data->dedu) ? $data->dedu : '0',
    ];

    $salaryHeadsCount = count($salaryHeads);
    $deductionsCount = count($deductions);
    $maxRows = max($salaryHeadsCount, $deductionsCount);
@endphp

@php
    $empleaves = $data->employee->employee_monthly_salaries_attend->first();
    $leav_cas =
        (!empty($empleaves->total_casual) ? @$empleaves->total_casual : '0') -
        (!empty($empleaves->bal_casual) ? @$empleaves->bal_casual : '0');

    $leav_anul =
        (!empty($empleaves->total_annual) ? @$empleaves->total_annual : '0') -
        (!empty($empleaves->bal_annual) ? @$empleaves->bal_annual : '0');
@endphp
{{-- <div style="border: 4px solid black;">
        <table style="border: 4px solid black; text-align: left!important; width: 100%;">
            <tbody style="border: 4px solid black;">
                <!-- Employee Information Section -->
                <tr>
                    <td></td>
                    <td colspan="13" style="border: 1px solid black; border-bottom: 0px solid white;"></td>
                </tr>
                <tr style="border: 1px solid black;">
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="font-weight: bold; font-size: 9px;">Employee#</td>
                    <td style="text-align: left; font-size: 9px;">{{ $data->employee->id ?? 'N/A' }}</td>
                    <td colspan="2"></td>
                    <td style="font-weight: bold; font-size: 9px;">Leaves Balances</td>
                    <td style="font-weight: bold; font-size: 9px; text-align: center;">C/L</td>
                    <td style="font-weight: bold; font-size: 9px; text-align: center;">A/L</td>
                    <td></td>
                    <td style="font-weight: bold; font-size: 9px;">Payment Date</td>
                    <td></td>
                    <td style="font-size: 9px;">{{ \Carbon\Carbon::parse($data->payment_date)->format('d-M-y') }}</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="font-weight: bold; font-size: 9px;">Name</td>
                    <td colspan="2" style="font-size:9px;">{{ $data->employee->salute }}
                        {{ $data->employee->name ?? 'N/A' }}</td>
                    <td></td>
                    <td style="font-weight: bold; font-size: 9px;">O.Balance</td>
                    <td style="text-align: center; font-size: 9px;">
                        {{ !empty($empleaves->total_casual) ? @$empleaves->total_casual : '0' }}
                    </td>
                    <td style="text-align: center; font-size: 9px;">
                        {{ !empty($empleaves->total_annual) ? @$empleaves->total_annual : '0' }}
                    </td>
                    <td></td>
                    <td style="font-weight: bold; font-size: 9px;">Mode</td>
                    <td></td>
                    <td style="font-size:9px;">
                        {{ $datas[0]->employee->employee_payscale_details[0]['paymode'] ?? 'N/A' }}
                    </td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="font-weight: bold; font-size: 9px;">Corporate Title</td>
                    <td style="font-size:9px;">{{ $data->employee->designation->name ?? 'N/A' }}</td>
                    <td colspan="2"></td>
                    <td style="font-weight: bold; font-size: 9px;">Leaves</td>
                    <td style="text-align: center; font-size: 9px;">{{ !empty($leav_cas) ? @$leav_cas : '0' }}</td>
                    <td style="text-align: center; font-size: 9px;">{{ !empty($leav_anul) ? @$leav_anul : '0' }}</td>
                    <td></td>
                    <td style="font-weight: bold; font-size: 9px;">Bank/Branch</td>
                    <td></td>
                    <td style="font-size:9px;">{{ $data->employee->bank_account->branch_name ?? 'N/A' }}</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="font-weight: bold; font-size: 9px;">Department</td>
                    <td style="font-size:9px;">{{ $data->employee->department->name ?? 'N/A' }}</td>
                    <td colspan="2"></td>
                    <td style="font-weight: bold; font-size: 9px;">C.Balance</td>
                    <td style="text-align: center; font-size: 9px;">{{ $data->leaves->cl_balance ?? 0 }}</td>
                    <td style="text-align: center;">{{ $data->leaves->al_balance ?? 0 }}</td>
                    <td></td>
                    <td style="font-weight: bold; font-size: 9px;">N.T.N</td>
                    <td></td>
                    <td style="font-size:9px;">{{ $data->employee->ntn ?? 'N/A' }}</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td colspan="4"></td>
                    <td style="font-weight: bold; font-size: 9px;">Working Days</td>
                    <td style="text-align: center; font-size: 9px;">
                        {{ !empty($data->sal_days) ? @$data->sal_days : '' }}
                    </td>
                    <td colspan="6" style="border-right: 1px solid black;"></td>
                </tr>

                <!-- Table Headers -->
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td colspan="3"
                        style="text-align: center; font-weight: bold; border: 1px solid black; font-size: 9px;">EARNINGS
                    </td>
                    <td></td>
                    <td colspan="3"
                        style="text-align: center; font-weight: bold; border: 1px solid black; font-size: 9px;">
                        DEDUCTIONS
                    </td>
                    <td></td>
                    <td colspan="3"
                        style="text-align: center; font-weight: bold; border: 1px solid black; font-size: 9px;"></td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">Gross
                        Salary</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        Y.T.D</td>
                    <td></td>
                    <td style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">Heads
                    </td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        P.M</td>
                    <td
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; text-align: center; font-size: 9px;">
                        Y.T.D</td>
                    <td></td>
                    <td colspan="3"
                        style="background: #d8d8d8; border: 1px solid black; font-weight: bold; font-size: 9px;">
                        Employee
                        Security Balance Y.T.D</td>
                    <td style="border-right: 1px solid black;"></td>
                </tr>

                <!-- Earnings & Deductions Body -->
                @php
                    $earnings = [];
                    $totalEarningsPM = 0;

                    foreach ($salaryHeads as $head) {
                        $value = 0;
                        $found = false;
                        foreach ($data->salary_heads as $empSalHead) {
                            if ($head->id == $empSalHead->head_id) {
                                $value = $empSalHead->head_value;
                                $found = true;
                                break;
                            }
                        }

                        if ($head->head == 'Initial Basic') {
                            $value = $data->basics ?? 0;
                            $found = true;
                        }

                        if ($found) {
                            $earnings[] = [
                                'name' => $head->head,
                                'pm' => $value,
                                'ytd' => $value * 3, // Placeholder YTD calculation
                            ];
                            $totalEarningsPM += $value;
                        }
                    }

                    // Add other earnings
                    $otherEarnings = $data->other1 ?? 0;
                    if ($otherEarnings) {
                        $earnings[] = [
                            'name' => 'Other',
                            'pm' => $otherEarnings,
                            'ytd' => $otherEarnings * 3,
                        ];
                        $totalEarningsPM += $otherEarnings;
                    }

                    // Add Stop Salary to deductions
                    $deductions['Stop Salary'] = $data->stop_sal ?? 0;
                @endphp

                @for ($i = 0; $i < $maxRows; $i++)
                    <tr>
                        <td></td>
                        <td style="border-left: 1px solid black;"></td>

                        <!-- Earnings Column -->
                        @if (isset($earnings[$i]))
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-size: 9px; font-weight: bold;">
                                {{ $earnings[$i]['name'] }}
                            </td>
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-size: 9px; text-align: right;">
                                {{ number_format($earnings[$i]['pm'], 0) }}
                            </td>
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-size: 9px; text-align: right;">
                                {{ number_format($earnings[$i]['ytd'], 0) }}
                            </td>
                        @else
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-size: 9px;">
                            </td>
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-size: 9px;">
                            </td>
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-size: 9px;">
                            </td>
                        @endif

                        <td></td>

                        <!-- Deductions Column -->
                        @if ($i < count($deductions))
                            @php
                                $deductionName = array_keys($deductions)[$i];
                                $deductionValue = $deductions[$deductionName];
                            @endphp
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; font-weight: bold;">
                                {{ $deductionName }}
                            </td>
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                                {{ number_format($deductionValue, 0) }}
                            </td>
                            <td
                                style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px; text-align: right;">
                                {{ number_format($deductionValue * 3, 0) }} <!-- Placeholder YTD -->
                            </td>
                        @else
                            <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">
                            </td>
                            <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">
                            </td>
                            <td style="border-left: 1px solid black; border-right: 1px solid black; font-size: 9px;">
                            </td>
                        @endif

                        <td></td>

                        <!-- Employee Security Balance -->
                        @if ($i == 1)
                            <td style="border-left: 1px solid black; font-weight: bold; font-size: 9px;">Employee
                                Security
                            </td>
                            <td></td>
                            <td style="text-align: right; border-right: 1px solid black; font-size: 9px;">
                                {{ number_format($data->emp_sec_ytd_balance ?? 0, 0) }}
                            </td>
                        @elseif ($i === 0)
                            <td style="border-left: 1px solid black;"></td>
                            <td></td>
                            <td style=" border-right: 1px solid black;"></td>
                        @elseif ($i === 2)
                            <td style="border-left: 1px solid black; border-bottom: 1px solid black;"></td>
                            <td style="border-bottom: 1px solid black;"></td>
                            <td style=" border-right: 1px solid black; border-bottom: 1px solid black;"></td>
                        @else
                            <td></td>
                            <td></td>
                            <td></td>
                        @endif
                        <td style="border-right: 1px solid black;"></td>
                    </tr>
                @endfor

                @php
                    $net_deduction =
                        (!empty($data->emp_sec) ? @$data->emp_sec : '0') +
                        (!empty($data->it) ? @$data->it : '0') +
                        (!empty($payscale->advance) ? @$payscale->advance : '0') +
                        (!empty($data->eobi) ? @$data->eobi : '0') +
                        (!empty($data->pessi) ? @$data->pessi : '0') +
                        (!empty($data->dedu) ? @$data->dedu : '0') +
                        (!empty($data->loan_emp_sec) ? @$data->loan_emp_sec : '0');
                    $total_payable = (!empty($data->gross) ? @$data->gross : '0') - $net_deduction;
                @endphp
                <!-- Gross Salary Row -->
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="border: 1px solid black; font-weight: bold;">Gross Rs.</td>
                    <td style="border: 1px solid black; text-align: right;">
                        {{ number_format($totalEarningsPM, 0) }}
                    </td>
                    <td style="border: 1px solid black; text-align: right;">
                        {{ number_format($totalEarningsPM * 3, 0) }} <!-- Placeholder YTD -->
                    </td>
                    <td></td>
                    <td style="border: 1px solid black; font-weight: bold;">Total Rs.</td>
                    <td style="border: 1px solid black; text-align: right;">
                        {{ !empty($total_payable) ? @$total_payable : '0' }}</td>
                    <td style="border: 1px solid black; text-align: right;">
                        {{ !empty($total_payable) ? @$total_payable * 3 : '0' }}</td>
                    <td colspan="5" style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td colspan="13" style="border-left: 1px solid black; border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td style="border-left: 1px solid black;"></td>
                    <td style="font-weight: bold; text-align: right;" colspan="6">Total Amount
                        Disbursed Rs.</td>
                    <td style="font-weight: bold; text-align: right; border: 4px solid black;">
                        {{ number_format($totalEarningsPM, 0) }}</td>
                    <td colspan="5" style="border-right: 1px solid black;"></td>
                </tr>
                <tr>
                    <td></td>
                    <td colspan="13" style="border: 1px solid black; border-top: 0px solid white;"></td>
                </tr>
            </tbody>
        </table>
    </div> --}}
