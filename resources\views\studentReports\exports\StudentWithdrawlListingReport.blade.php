@include('student.exports.header')

<table border="1" cellpadding="2" cellspacing="0" style="width:100%; border-collapse:collapse; font-family:<PERSON><PERSON><PERSON>, <PERSON>l, sans-serif; font-size:8px; table-layout:fixed;">
    <thead>
        <tr style="background:#e6e6e6; border: 2px solid black;">
            <th style="width:28px; font-size:8px; font-family:<PERSON>ibri; background:gray; text-align:center; border: 2px solid black;">Sr#</th>
            <th style="width:32px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">B.Sr#</th>
            <th style="width:32px; font-size:8px; font-family:Cal<PERSON>ri; background:gray; text-align:center; border: 2px solid black;">Roll #</th>
            <th style="width:110px; font-size:8px; font-family:<PERSON><PERSON>ri; background:gray; text-align:center; border: 2px solid black;">Student Name</th>
            <th style="width:40px; font-size:8px; font-family:<PERSON><PERSON><PERSON>; background:gray; text-align:center; border: 2px solid black;">Class</th>
            <th style="width:110px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Father Name</th>
            <th style="width:100px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Address</th>
            <th style="width:70px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Phone#</th>
            <th style="width:70px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Withdrawal Date</th>
            <th style="width:100px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Reason of Leaving</th>
            <th style="width:30px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">WO#</th>
            <th style="width:60px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Security Deposit</th>
            <th style="width:60px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Net Receivable</th>
            <th style="width:60px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Net Paid</th>
            <th style="width:60px; font-size:8px; font-family:Calibri; background:gray; text-align:center; border: 2px solid black;">Net Payable</th>
        </tr>
    </thead>
    <tbody>
        @php $overall_sr = 1; @endphp
        @foreach ($all_data as $branchId => $students)
            <tr style="background:gray; color:black; font-size:8px; font-family:Calibri; text-align:left; border: 2px solid black;">
                <td colspan="15" style="font-size:8px; background:gray; font-family:Calibri; color: black; text-align:left; border: 2px solid black; background:rgb(223, 223, 223); color:black;">{{ $branches[$branchId] ?? 'Branch Not Specified' }}</td>
            </tr>
            @php $branch_sr = 1; @endphp
            @foreach ($students as $data)
                <tr>
                    <td style="text-align:center; width:30px; font-size:8px; font-family:calibri;">{{ $overall_sr++ }}</td>
                    <td style="text-align:center; width:30px; font-size:8px; font-family:calibri;">{{ $branch_sr++ }}</td>
                    <td style="text-align:center; width:50px; font-size:8px; font-family:calibri;">{{ !empty($data) ? $data->enrollId : '-' }}</td>
                    <td style="text-align:left; width:100px; word-break:break-word; white-space:normal; font-size:8px; font-family:calibri;">
                        {!! nl2br(wordwrap(!empty($data->StudentRegistration) ? $data->StudentRegistration->stdname : '-', 20, "\n", true)) !!}
                    </td>
                    <td style="text-align:left; width:75px; font-size:8px; font-family:calibri;">
                        {{ !empty($data->StudentRegistration->class) ? $data->StudentRegistration->class->name : '-' }}
                    </td>
                    <td style="text-align:left; width:100px; word-break:break-word; white-space:normal; font-size:8px; font-family:calibri;">
                        {!! nl2br(wordwrap(!empty($data->StudentRegistration) ? $data->StudentRegistration->fathername : '-', 20, "\n", true)) !!}
                    </td>
                    <td style="text-align:left; width:100px; word-break:break-word; white-space:normal; font-size:8px; font-family:calibri;">
                        {!! nl2br(wordwrap(!empty($data->StudentRegistration) ? $data->StudentRegistration->address : '-', 20, "\n", true)) !!}
                    </td>
                    <td style="text-align:center; width:75px; word-break:break-word; white-space:normal; font-size:8px; font-family:calibri;">
                        @php
                            $phones = !empty($data->StudentRegistration) ? $data->StudentRegistration->fatherphone : '-';
                            if ($phones && $phones !== '-') {
                                $phonesArr = array_map('trim', explode(',', $phones));
                                echo implode(',<br>', $phonesArr);
                            } else {
                                echo '-';
                            }
                        @endphp
                    </td>
                    <td style="text-align:center; width:80px; font-size:8px; font-family:calibri;">
                        {{ !empty($data->withdrawal) ? $data->withdrawal->withdraw_date : '-' }}
                    </td>
                    <td style="text-align:left; width:100px; word-break:break-word; white-space:normal; font-size:8px; font-family:calibri;">
                        {!! nl2br(wordwrap(!empty($data->withdrawal) ? $data->withdrawal->reason : '-', 20, "\n", true)) !!}
                    </td>
                    <td style="text-align:center; width:80px; font-size:8px; font-family:calibri;">{{ !empty($data->withdrawal) ? $data->withdrawal->wo_no : '-' }}</td>
                    <td style="text-align:right; width:80px; font-size:8px; font-family:calibri;">{{ !empty($data->withdrawal) ? number_format($data->withdrawal->security_deposit, 2) : '-' }}</td>
                    <td style="text-align:right; width:80px; font-size:8px; font-family:calibri;">{{ !empty($data->withdrawal) ? number_format($data->withdrawal->receivable, 2) : '-' }}</td>
                    <td style="text-align:right; width:80px; font-size:8px; font-family:calibri;">{{ !empty($data->withdrawal) ? number_format($data->withdrawal->paid, 2) : '-' }}</td>
                    <td style="text-align:right; width:80px; font-size:8px; font-family:calibri;">{{ !empty($data->withdrawal) ? number_format($data->withdrawal->payable, 2) : '-' }}</td>


                </tr>
            @endforeach
            <tr>
                <td colspan="11" style="font-size:8px; font-family:Calibri; text-align:center; border: 2px solid black; background:gray; font-weight:bold;">Branch Total</td>
                <td style="text-align:right; border: 2px solid black; background:gray;">{{ $branchTotals[$branchId]['security_deposit'] ?? 0 }}</td>
                <td style="text-align:right; border: 2px solid black; background:gray;">{{ $branchTotals[$branchId]['receivable'] ?? 0 }}</td>
                <td style="text-align:right; border: 2px solid black; background:gray;">{{ $branchTotals[$branchId]['paid'] ?? 0 }}</td>
                <td style="text-align:right; border: 2px solid black; background:gray;">{{ $branchTotals[$branchId]['payable'] ?? 0 }}</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 10px; border: none;"></td>
                <td colspan="15" style="height: 10px; border: none;"></td>
                <td colspan="15" style="height: 10px; border: none;"></td>
            </tr>
        @endforeach
        <tr>
            <td colspan="11" style="font-size:8px; font-family:Calibri; text-align:center; background:gray; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; font-weight:bold;">Grand Total</td>
            <td style="text-align:right; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; background:gray; font-weight:bold;">{{ $grandTotals['security_deposit'] ?? 0 }}</td>
            <td style="text-align:right; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; background:gray; font-weight:bold;">{{ $grandTotals['receivable'] ?? 0 }}</td>
            <td style="text-align:right; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; background:gray; font-weight:bold;">{{ $grandTotals['paid'] ?? 0 }}</td>
            <td style="text-align:right; border: 2px solid black; border-top: 2px double black; border-bottom: 2px double black; background:gray; font-weight:bold;">{{ $grandTotals['payable'] ?? 0 }}</td>
        </tr>
    </tbody>
</table>

@include('student.exports.footer')
