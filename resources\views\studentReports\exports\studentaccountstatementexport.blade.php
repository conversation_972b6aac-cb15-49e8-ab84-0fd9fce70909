@include('student.exports.header')
<div style="font-family: Arial, Helvetica, sans-serif; font-size: 8px;">

    {{-- Student Details Section --}}

        <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border-bottom: 20px solid black;">
            <thead>
                <tr>
                    <th class="header-column"></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th class="value-column"></th>
                    <th class="empty-columns"></th>
                    <th class="empty-columns"></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Student Name</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">HADIA BATOOL</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Roll #</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">1234</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Account Title</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">HADIA BATOOL d/o Mr AHMED KAZMI</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Branch Name :</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">I-8 Nursery Islamabad</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">From Date :</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">01-May-2025 To 30-Aug-2025</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">User ID :</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;"><EMAIL></td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Opening Receivable as of 01-May-2025 :</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">5000</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Closing Receivable as of 30-Aug-2025 :</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">0</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
                <tr>
                    <td class="header-column" style="font-family: calibri; font-size:8px; font-weight:bold;">Statement run date and time :</td>
                    <td></td>
                    <td></td>
                    <th></th>
                    <th></th>
                    <td class="value-column" style="text-align: left; font-family: calibri; font-size:8px;">22-Aug-2025 07:12:47 AM</td>
                    <td class="empty-columns"></td>
                    <td class="empty-columns"></td>
                </tr>
            </tbody>
        </table>


    {{-- Main Data Table --}}
    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
        <thead>
            <tr style="background-color: gray; color: white;">
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Sr #</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Date</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Class</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Billing Month</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Challan #</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">VT</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Mode</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Debit</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Credit</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Balance</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">T.Head</th>
                <th style="border: 1px solid black; padding: 4px; text-align: center; font-size: 8px;">Reference</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalDebit = 0;
                $totalCredit = 0;
                $totalBalance = 0;
            @endphp
            @foreach ($records as $i => $row)
                @php
                    $totalDebit += $row->debit ?? 0;
                    $totalCredit += $row->credit ?? 0;
                    $totalBalance += $row->balance ?? 0;
                @endphp
                <tr>
                    <td style="border: 1px dotted black; padding: 4px; text-align: center; font-size: 8px;">{{ $i+1 }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->date ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->class ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->billing_month ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->challan_no ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->vt ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->mode ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: right; font-size: 8px;">{{ $row->debit ?? '0' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: right; font-size: 8px;">{{ $row->credit ?? '0' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: right; font-size: 8px;">{{ $row->balance ?? '0' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->thead ?? '-' }}</td>
                    <td style="border: 1px dotted black; padding: 4px; text-align: left; font-size: 8px;">{{ $row->reference ?? '-' }}</td>
                </tr>
            @endforeach
            <tr style="background-color: #B8B8B8;">
                <td colspan="7" style="border: 1px dotted black; border-bottom: 20px solid black; padding: 4px; font-weight: bold; font-size: 8px; background-color: #B8B8B8;">Total</td>
                <td style="border: 1px dotted black; border-bottom: 20px solid black; padding: 4px; font-weight: bold; text-align: right; font-size: 8px; background-color: #B8B8B8;">{{ $totalDebit }}</td>
                <td style="border: 1px dotted black; border-bottom: 20px solid black; padding: 4px; font-weight: bold; text-align: right; font-size: 8px;"></td>
                <td style="border: 1px dotted black; border-bottom: 20px solid black; padding: 4px; font-weight: bold; text-align: right; font-size: 8px;"></td>
                <td colspan="2" style="border: 1px dotted black; border-bottom: 20px solid black; padding: 4px; font-size: 8px;"></td>
            </tr>
            {{-- <tr style="background-color: #8B8B8B;">
                <td colspan="7" style="border: 1px dotted black; padding: 4px; font-weight: bold; font-size: 8px;">Grand Total</td>
                <td style="border: 1px dotted black; padding: 4px; font-weight: bold; text-align: right; font-size: 8px;">{{ $totalDebit }}</td>
                <td style="border: 1px dotted black; padding: 4px; font-weight: bold; text-align: right; font-size: 8px;">{{ $totalCredit }}</td>
                <td style="border: 1px dotted black; padding: 4px; font-weight: bold; text-align: right; font-size: 8px;">{{ $totalBalance }}</td>
                <td colspan="2" style="border: 1px dotted black; padding: 4px; font-size: 8px;"></td>
            </tr> --}}
        </tbody>
    </table>
</div>
@include('student.exports.footer')