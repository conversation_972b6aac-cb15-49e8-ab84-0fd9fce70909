<table class="table">
    <thead class="table_heads">
        @include('student.exports.header')
        <tr>
            <td style="font-size: 11px; font-weight: bold;">Period</td>
            <td style="font-size: 11px; font-weight: bold;">From</td>
            <td colspan="2" style="border-bottom: 1px solid black;"></td>
            <td colspan="2" style=""></td>
            <td style="font-size: 11px; font-weight: bold;">To</td>
            <td colspan="2" style="border-bottom: 1px solid black;"></td>
        </tr>
        <tr></tr>
        <tr>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Sr. No') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Branch') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Emp No') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Emp Name') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Casual Leave Opening Bal') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Leave Availed') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Casual Leave Closing Bal') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Last Year Bal B/F') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Annual Leave Opening Balance') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Leave Availed') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Annual Leave Closing Bal') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('ML OB') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('ML Avail') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('ML CB') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Unpaid') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Unpaid Total') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Total Availed') }}</th>
            <th style="text-align: center; font-weight: bold; font-size: 8px; background-color: gray; border: 1px solid black;">{{ __('Total Balance') }}</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($reportData as $branchName => $employeesData)
            {{-- <tr class="table-secondary">
                <td colspan="18">{{ $branchName }}</td>
            </tr> --}}
            @foreach ($employeesData as $idx => $item)
                @php $emp = $item['employee']; @endphp
                <tr>
                    <td style="border: 1px solid black;">{{ $loop->iteration }}</td>
                    <td style="border: 1px solid black;">{{ $branchName }}</td>
                    <td style="border: 1px solid black;">{{ $emp->employee_id }}</td>
                    <td style="border: 1px solid black;">{{ $emp->salute }} {{ $emp->name }}</td>
                    <!-- Casual Leave -->
                    <td style="border: 1px solid black;">{{ $item['ob']['CL'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['availed']['CL'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['cb']['CL'] }}</td>
                    <!-- Last Year Bal B/F - Unavailable -->
                    <td style="border: 1px solid black;">-</td>
                    <!-- Annual Leave -->
                    <td style="border: 1px solid black;">{{ $item['ob']['AL'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['availed']['AL'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['cb']['AL'] }}</td>
                    <!-- Medical Leave -->
                    <td style="border: 1px solid black;">{{ $item['ob']['ML'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['availed']['ML'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['cb']['ML'] }}</td>
                    <!-- Unpaid Leave -->
                    <td style="border: 1px solid black;">{{ $item['availed']['Unpaid'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['availed']['Unpaid'] }}</td>
                    <!-- Totals -->
                    <td style="border: 1px solid black;">{{ $item['totalAvailed'] }}</td>
                    <td style="border: 1px solid black;">{{ $item['totalBalance'] }}</td>
                </tr>
            @endforeach
        @endforeach
        @include('student.exports.footer')
    </tbody>
</table>