<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Fee Challan Report</title>
</head>
<body>
    @php
        $i = 1;
        $grandTotal = [
            'total' => 0,
            'net' => 0,
            'discount' => 0,
            'monthly_fee' => 0,
            'arrears' => 0
        ];
        $grandHeadTotals = [];
    @endphp

    @include('student.exports.header')
    
    <table>
        <thead>
            <tr>
                <th colspan="7">Student Information</th>
                <th>Monthly Fee</th>
                @foreach ($heads as $head)
                    <th colspan="3">{{ $head->fee_head }}</th>
                @endforeach
                <th colspan="2">Current Month Bill</th>
                <th colspan="2">Discount</th>
            </tr>
            <tr>
                <th>Sr.</th>
                <th>B Sr.</th>
                <th>Roll No</th>
                <th>Student Name</th>
                <th>Reg type</th>
                <th>Class</th>
                <th>Billing Month</th>
                <th>Rs.</th>
                @foreach ($heads as $head)
                    <th>Rs.</th>
                    <th>Disc.</th>
                    <th>Rs.</th>
                @endforeach
                <th>Arrears</th>
                <th>Net Receivable</th>
                <th>Discount %</th>
                <th>Category</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($report as $branchId => $students)
            <tr>
                <td colspan="4" style="font-weight: bold; text-align: left; background-color:#bcbcbc; border: 1px 0px 1px 1px solid #000; white-space: nowrap; overflow: visible; padding: 0;">
                    {{ $branches[$branchId] ?? 'Branch Not Specified' }}
                </td>
                <td colspan="{{ 8 + ($heads->count() * 3) }}" style="font-weight: bold; text-align: left; background-color:#bcbcbc; border: 1px 1px 1px 0px solid #000; white-space: nowrap; overflow: visible; padding: 0;">
                </td>
            </tr>
                @php
                    $branchTotal = [
                        'total' => 0,
                        'net' => 0,
                        'discount' => 0,
                        'monthly_fee' => 0,
                        'arrears' => 0
                    ];
                    $branchHeadTotals = collect($heads)
                        ->mapWithKeys(fn($h) => [$h->id => [
                            'price' => 0, 
                            'disc' => 0, 
                            'net' => 0
                        ]])->toArray();
                @endphp

                @foreach ($students as $index => $stu)
                    @php
                        $stu = array_merge([
                            'total' => 0,
                            'net' => 0,
                            'discount' => 0,
                            'monthly_fee' => 0,
                            'arrears' => 0,
                            'fee_month' => '',
                            'details' => []
                        ], $stu);
                    @endphp
                    <tr>
                        <td>{{ $i++ }}</td>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $stu['roll'] }}</td>
                        <td>{{ $stu['name'] }}</td>
                        <td>{{ $stu['reg'] }}</td>
                        <td>{{ $stu['class'] }}</td>
                        <td>{{ $stu['fee_month'] }}</td>
                        <td>{{ $stu['monthly_fee'] }}</td>

                        @foreach ($heads as $head)
                            @php
                                $h = array_merge([
                                    'price' => 0,
                                    'disc' => 0,
                                    'net' => 0
                                ], $stu['details'][$head->id] ?? []);
                                
                                $branchHeadTotals[$head->id]['price'] += $h['price'];
                                $branchHeadTotals[$head->id]['disc'] += $h['disc'];
                                $branchHeadTotals[$head->id]['net'] += $h['net'];
                                
                                if (!isset($grandHeadTotals[$head->id])) {
                                    $grandHeadTotals[$head->id] = [
                                        'price' => 0,
                                        'disc' => 0,
                                        'net' => 0
                                    ];
                                }
                                $grandHeadTotals[$head->id]['price'] += $h['price'];
                                $grandHeadTotals[$head->id]['disc'] += $h['disc'];
                                $grandHeadTotals[$head->id]['net'] += $h['net'];
                            @endphp
                            <td>{{ $h['price'] }}</td>
                            <td>{{ $h['disc'] }}</td>
                            <td>{{ $h['net'] }}</td>
                        @endforeach
                        
                        <td>{{ $stu['arrears'] }}</td>
                        <td>{{ $stu['net'] }}</td>
                        <td>{{ $stu['discount'] }}</td>
                        <td>{{ $stu['category'] ?? '' }}</td>
                        
                        @php
                            $branchTotal['total'] += $stu['total'];
                            $branchTotal['discount'] += $stu['discount'];
                            $branchTotal['net'] += $stu['net'];
                            $branchTotal['monthly_fee'] += $stu['monthly_fee'];
                            $branchTotal['arrears'] += $stu['arrears'];

                            $grandTotal['total'] += $stu['total'];
                            $grandTotal['discount'] += $stu['discount'];
                            $grandTotal['net'] += $stu['net'];
                            $grandTotal['monthly_fee'] += $stu['monthly_fee'];
                            $grandTotal['arrears'] += $stu['arrears'];
                        @endphp
                    </tr>
                @endforeach

                <!-- Branch Total Row -->
                <tr>
                    <td colspan="7" style="background:gray; font-size: 8px; text-align: center; border: 1px solid black;">Branch Total</td>
                    <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchTotal['monthly_fee'] }}</td>
                    @foreach ($heads as $head)
                        <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchHeadTotals[$head->id]['price'] ?? 0 }}</td>
                        <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchHeadTotals[$head->id]['disc'] ?? 0 }}</td>
                        <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchHeadTotals[$head->id]['net'] ?? 0 }}</td>
                    @endforeach
                    <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchTotal['arrears'] }}</td>
                    <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchTotal['net'] }}</td>
                    <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;">{{ $branchTotal['discount'] }}</td>
                    <td style="background:gray; font-size: 8px; text-align: right; border: 1px solid black;"></td>
                </tr>
                
                <tr>
                    <td colspan="{{ ($heads->count() * 3) + 12 }}"></td>
                </tr>
                <tr>
                    <td colspan="{{ ($heads->count() * 3) + 12 }}"></td>
                </tr>
            @endforeach

            <!-- Grand Total Row -->
            <tr>
                <td colspan="7" style="background:gray; font-size: 8px; border: 1px solid black; text-align: center; border-top: 1px double black; border-bottom: 1px double black;">Grand Total</td>
                <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandTotal['monthly_fee'] }}</td>
                @foreach ($heads as $head)
                    <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandHeadTotals[$head->id]['price'] ?? 0 }}</td>
                    <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandHeadTotals[$head->id]['disc'] ?? 0 }}</td>
                    <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandHeadTotals[$head->id]['net'] ?? 0 }}</td>
                @endforeach
                <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandTotal['arrears'] }}</td>
                <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandTotal['net'] }}</td>
                <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;">{{ $grandTotal['discount'] }}</td>
                <td style="background:gray; font-size: 8px; border: 1px solid black; text-align: right; border-top: 1px double black; border-bottom: 1px double black;"></td>
            </tr>
        </tbody>
    </table>
    
    @include('student.exports.footer')
</body>
</html>