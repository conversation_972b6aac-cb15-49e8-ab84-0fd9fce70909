<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use App\Models\Challan;
use App\Models\ChallanHead;
use App\Models\Challans;
use App\Models\ChartOfAccount;
use App\Models\Classes;
use App\Models\ClassSection;
use App\Models\Concession;
use App\Models\ConcessionPolicy;
use App\Models\ConcessionPolicyHead;
use App\Models\FeeHead;
use App\Models\JournalEntry;
use App\Models\JournalItem;
use App\Models\Registring_option;
use App\Models\StudentEnrollments;
use App\Models\StudentFeeStructure;
use App\Models\StudentReceipt;
use App\Models\StudentRegistration;
use App\Models\StudentTransfer;
use App\Models\StudentWithdrawal;
use App\Models\Utility;
use App\Models\Section;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
class DataImportController extends Controller
{
    public function showForm()
    {
        Session::forget('counter');
        return view('data_import.form');
    }
    public function importData(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:csv,txt',
            'data_type' => 'required|string',
        ]);

        $file = $request->file('excel_file');
        $dataType = $request->input('data_type');
        set_time_limit(0);

        if ($dataType == 'enrollment') {
            return $this->EnrollmentImport($file, $request);
        } else if ($dataType == 'enrollment_status') {
            return $this->EnrollmentStatus($file, $request);
        } else if ($dataType == 'enrollment2') {
            return $this->EnrollmentImport2($file, $request);
        } else if ($dataType == 'registration2') {
            return $this->RegistrationImport2($file, $request);
        } else if ($dataType == 'class') {
            return $this->ClassImport($request);
        } else if ($dataType == 'section') {
            return $this->SectionImport($request);
        } else if ($dataType == 'registration') {
            return $this->RegistrationImport($file, $request);
        } else if ($dataType == 'student_detail') {
            return $this->StudentDetail($file, $request);
        } else if ($dataType == 'transfer') {
            return $this->TransferImport($file, $request);
        } else if ($dataType == 'withdraw') {
            return $this->WithdrawImport($file, $request);
        } else if ($dataType == 'regular_challan') {
            return $this->RegularChallanImport($file, $request);
        } else if ($dataType == 'security') {
            return $this->SecurityChallanImport($file, $request);
        } else if ($dataType == 'receipts') {
            return $this->ReceiptsImport($file, $request);
        } else if ($dataType == 'concession') {
            return $this->ConcessionImport($file, $request);
        } else if ($dataType == 'concession_list') {
            return $this->ConcessionListImport($file, $request);
        } else {
            return redirect()->back()->with('error', 'No Data Type Selected');
        }
    }
    private function EnrollmentImport2($file, $request)
    {
        // dd($request->all());
        // new code for enrollment
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
                    if ($count > 0) {
                        // dd($all_data);
                        $record_status = 'Error';
                        $reason = '';
                        // widthdraw data 
                        // Validate enrollment
                        //     $enr = StudentEnrollments::where('enrollId', $all_data[1])->first();
                        //       $enr->active_status = '0';
                        //      $enr->save();
                        //     $withdrawal_date = date('Y-m-d', strtotime($all_data[0]));
                        //     if ($enr) {
                        //         $wdth = StudentWithdrawal::where('student_id', $enr->regId)->first();
                        //         if(!$wdth){
                        //             // Create new withdrawal
                        //             $withdrawal = new StudentWithdrawal();
                        //             $withdrawal->student_id = $enr->regId;
                        //             $withdrawal->challan_id = null;
                        //             $withdrawal->branch_id = 6;
                        //             $withdrawal->class_id = $enr->class_id;
                        //             $withdrawal->withdraw_date = $withdrawal_date;
                        //             $withdrawal->apply_date = $withdrawal_date;
                        //             $withdrawal->reason = "School Change"; // Modify accordingly
                        //             $withdrawal->remark = "School Change"; // Modify accordingly
                        //             $withdrawal->owned_by = $enr->owned_by;
                        //             $withdrawal->created_by = \Auth::user()->creatorId();
                        //             $withdrawal->save();

                        //         }else{
                        //             $reason = 'Student already withdrawn';
                        //             $error_counter++;
                        //             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        //             $count++;
                        //             continue;
                        //         }
                        //     } else {
                        //         $reason = 'Enrollment not found';
                        //         $error_counter++;
                        //         $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        //         $count++;
                        //         continue;
                        //     }
                        // $enr->active_status = '0';
                        // $enr->save();
                        // $reg = StudentRegistration::where('roll_no', $all_data[1])->first();
                        // $reg->student_status = 'withdrawl';
                        // $reg->save();
                        //  DB::commit();

                        // Proceed with withdrawal logic if no errors
                        // Add to successful records
                        // $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        // $success_counter++;
                        // widthdraw data 


                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $reason = 'Empty Roll No';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $reg = StudentRegistration::where('roll_no', $all_data[0])->first();
                        if (!$reg) {
                            $reg = StudentRegistration::where('reg_no', $all_data[6])->first();
                        }
                        if (!$reg) {
                            $reason = 'Registration not found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $branch = User::where('name', 'like', '%' . $all_data[3] . '%')->first();
                        if (!$branch) {
                            $reason = 'Branch not found: ' . $all_data[3];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        // Check for existing enrollment to avoid duplication

                        $class = Classes::where('name', 'like', '%' . $all_data[4] . '%')->where('owned_by', $branch->id)->first();
                        if (!$class) {
                            $reason = 'Class not found: ' . $all_data[4];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        if ($class) {
                            $section = Section::whereRaw('LOWER(name) like ?', ['%' . strtolower($all_data[5]) . '%'])->first();
                            if (!$section) {
                                $reason = 'Section not found: ' . $all_data[5];
                                $error_counter++;
                                $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                                $count++;
                                continue;
                            }
                        }
                        $sectionclass = ClassSection::where('class_id', $class->id)->where('section_id', $section->id)->where('owned_by', $branch->id)->where('active_status', 1)->first();
                        // dd($all_data,$class, $section);
                        if (!$sectionclass) {
                            $reason = 'Class Section not found: ' . $all_data[5];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $existingEnrollment = StudentEnrollments::where('regId', $reg->id)->first();
                        // if (!$existingEnrollment) {
                        //     $reason = 'existingEnrollment not found: ' . $all_data[0];
                        //     $error_counter++;
                        //     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        //     $count++;
                        //     continue;
                        // }
                        // $reg->dob = date('Y-m-d', strtotime($all_data[7]));
                        $reg->reg_no = $all_data[6];
                        $reg->stdname = $all_data[2];
                        $reg->class_id = $class->id;
                        $reg->branch = $branch->id;
                        $reg->owned_by = $branch->id;
                        $reg->student_status = 'Enrolled';
                        $reg->save();

                        $enrollment_date = date('Y-m-d', strtotime($all_data[1]));
                        if ($existingEnrollment) {
                            $existingEnrollment->enrollId = $all_data[0];
                            $existingEnrollment->regId = $reg->id;
                            $existingEnrollment->adm_date = date('Y-m-d', strtotime($all_data[1]));
                            $existingEnrollment->class_id = $class->id;
                            $existingEnrollment->section_id = $section->id;
                            $existingEnrollment->owned_by = $branch->id;
                            $existingEnrollment->active_status = 1;
                            // $existingEnrollment->adm_date = $enrollment_date;
                            $existingEnrollment->save();
                            // if ($existingEnrollment->enrollId != '' && $existingEnrollment->enrollId != null) {
                            //     $existingEnrollment->enrollId = $all_data[2];
                            //     $existingEnrollment->save();
                            // }
                            // $reason = 'Student already enrolled in this session';
                            // $duplication_counter++;
                            // $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            // $count++;
                            // continue;
                        } else {
                            // Create new enrollment
                            $newEnrollId = $all_data[0];
                            $created_at = date('Y-m-d H:i:s', strtotime($all_data[1]));

                            $enrollment = new StudentEnrollments();
                            $enrollment->enrollId = $newEnrollId;
                            $enrollment->regId = $reg->id;
                            $enrollment->adm_date = $enrollment_date;
                            $enrollment->class_id = $class->id;
                            $enrollment->section_id = $section->id;
                            $enrollment->adm_session = 2;
                            $enrollment->adm_branch = $branch->id;
                            $enrollment->session_id = 2;
                            $enrollment->owned_by = $branch->id;
                            $enrollment->active_status = 1;
                            $enrollment->created_by = \Auth::user()->creatorId();
                            $enrollment->created_at = $created_at;
                            $enrollment->updated_at = $created_at;
                            $enrollment->save();
                            $reg->roll_no = $newEnrollId;
                            $reg->save();
                        }
                        $existingEnrollment = StudentEnrollments::where('regId', $reg->id)->first();
                        if (!$existingEnrollment) {

                            $reason = 'existingEnrollment not found: ' . $all_data[0];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'enrollment_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Enrollment(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e, $all_data);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
        // set_time_limit(0);
        // $file = $request->file('excel_file');
        // $filename = $file->getClientOriginalName();
        // $file->move(public_path('assets/import/csv_file/'), $filename);
        // $filepath = public_path('assets/import/csv_file/' . $filename);

        // // Initialize counters and tracking arrays
        // $success_counter = 0;
        // $error_counter = 0;
        // $duplication_counter = 0;
        // $skip_data = [];
        // $processed_records = [];

        // DB::beginTransaction();
        // try {
        //     if (($handle = fopen($filepath, 'r')) !== FALSE) {
        //         $count = 0;

        //         while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
        //             if ($count > 0) {
        //                 $record_status = 'Error';
        //                 $reason = '';
        //                 // Check for empty data in first column
        //                 if (empty($all_data[2])) {
        //                     $reason = 'Empty enrollment ID';
        //                     $error_counter++;
        //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
        //                     $count++;
        //                     continue;
        //                 }
        //                 $reg = StudentRegistration::where('stdname', 'like', '%' . $all_data[3] . '%')
        //                     ->Where('fathername', 'like', '%' . $all_data[4] . '%')->first();
        //                 if (!$reg) {
        //                     $reason = 'Registration not found';
        //                     $error_counter++;
        //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
        //                     $count++;
        //                     continue;
        //                 }
        //                 // Check for existing enrollment to avoid duplication
        //                 $existingEnrollment = StudentEnrollments::where('regId', $reg->id)
        //                     ->first();

        //                 if ($existingEnrollment) {
        //                     if ($existingEnrollment->enrollId != '' && $existingEnrollment->enrollId != null) {
        //                         $existingEnrollment->enrollId = $all_data[2];
        //                         $existingEnrollment->save();
        //                     }
        //                     $reason = 'Student already enrolled in this session';
        //                     $duplication_counter++;
        //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
        //                     $count++;
        //                     continue;
        //                 }

        //                 // Proceed with enrollment logic if no errors
        //                 $enrollment_date = date('Y-m-d', strtotime($all_data[2]));
        //                 $branch = User::where('name', 'like', '%' . $all_data[1] . '%')->first();
        //                 if (!$branch) {
        //                     $reason = 'Branch not found: ' . $all_data[1];
        //                     $error_counter++;
        //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
        //                     $count++;
        //                     continue;
        //                 }
        //                 $class = Classes::where('name', 'like', '%' . $all_data[8] . '%')->where('owned_by', $branch->id)->first();
        //                 if (!$class) {
        //                     $reason = 'Class not found: ' . $all_data[8] . ' for branch: ' . $branch->name;
        //                     $error_counter++;
        //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
        //                     $count++;
        //                     continue;
        //                 }
        //                 $section = Section::where('name', 'like', '%' . $all_data[9] . '%')
        //                     ->where('owned_by', $branch->id)->first();
        //                 if (!$section) {
        //                     //create section
        //                     $section = new Section();
        //                     $section->name = $all_data[9];
        //                     $section->created_by = auth()->user()->id;
        //                     $section->owned_by = $branch->id;
        //                     $section->save();
        //                 }

        //                 // Create new enrollment
        //                 $newEnrollId = $all_data[0];
        //                 $created_at = date('Y-m-d H:i:s', strtotime($all_data[2]));

        //                 $enrollment = new StudentEnrollments();
        //                 $enrollment->enrollId = $newEnrollId;
        //                 $enrollment->regId = $reg->id;
        //                 $enrollment->adm_date = $enrollment_date;
        //                 $enrollment->class_id = $class->id;
        //                 $enrollment->section_id = $section->id;
        //                 $enrollment->session_id = $branch->id;
        //                 $enrollment->owned_by = $branch->id;
        //                 $enrollment->created_by = \Auth::user()->creatorId();
        //                 $enrollment->created_at = $created_at;
        //                 $enrollment->updated_at = $created_at;
        //                 $enrollment->save();
        //                 $reg->roll_no = $newEnrollId;
        //                 $reg->save();
        //                 // Add to successful records
        //                 $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
        //                 $success_counter++;
        //             } else {
        //                 // Save the header row with additional columns
        //                 $header = $all_data;
        //                 $header[] = 'Status';
        //                 $header[] = 'Reason';
        //                 $processed_records[] = $header;
        //             }
        //             $count++;
        //         }
        //         fclose($handle);
        //         DB::commit();
        //         if (!empty($skip_data)) {
        //             $export_filename = 'enrollment_errors_' . time() . '.csv';
        //             $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
        //             $error_file = fopen($error_filepath, 'w+');
        //             fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
        //             foreach ($skip_data as $row) {
        //                 fputcsv($error_file, $row);
        //             }
        //             fclose($error_file);
        //             return response()->download($error_filepath)->deleteFileAfterSend(true);
        //         }
        //         return redirect()->back()->with('message', "{$success_counter} Enrollment(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
        //     }
        // } catch (\Exception $e) {
        //     DB::rollBack();
        //     dd($e);
        //     return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        // }
    }

    //  private function EnrollmentImport2($file, $request)
    // {
    //     // dd($request->all());
    //     // new code for enrollment
    //     set_time_limit(0);
    //     $file = $request->file('excel_file');
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // Initialize counters and tracking arrays
    //     $success_counter = 0;
    //     $error_counter = 0;
    //     $duplication_counter = 0;
    //     $skip_data = [];
    //     $processed_records = [];

    //     DB::beginTransaction();
    //     try {
    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             $count = 0;

    //             while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
    //                 if ($count > 0) {
    //                     // dd($all_data);
    //                     $record_status = 'Error';
    //                     $reason = '';
    //                         // widthdraw data 
    //                         // Validate enrollment
    //                             $enr = StudentEnrollments::where('enrollId', $all_data[1])->first();
    //                               $enr->active_status = '0';
    //                              $enr->save();
    //                             $withdrawal_date = date('Y-m-d', strtotime($all_data[0]));
    //                             if ($enr) {
    //                                 $wdth = StudentWithdrawal::where('student_id', $enr->regId)->first();
    //                                 if(!$wdth){
    //                                     // Create new withdrawal
    //                                     $withdrawal = new StudentWithdrawal();
    //                                     $withdrawal->student_id = $enr->regId;
    //                                     $withdrawal->challan_id = null;
    //                                     $withdrawal->branch_id = 6;
    //                                     $withdrawal->class_id = $enr->class_id;
    //                                     $withdrawal->withdraw_date = $withdrawal_date;
    //                                     $withdrawal->apply_date = $withdrawal_date;
    //                                     $withdrawal->reason = "School Change"; // Modify accordingly
    //                                     $withdrawal->remark = "School Change"; // Modify accordingly
    //                                     $withdrawal->owned_by = $enr->owned_by;
    //                                     $withdrawal->created_by = \Auth::user()->creatorId();
    //                                     $withdrawal->save();

    //                                 }else{
    //                                     $reason = 'Student already withdrawn';
    //                                     $error_counter++;
    //                                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                                     $count++;
    //                                     continue;
    //                                 }
    //                             } else {
    //                                 $reason = 'Enrollment not found';
    //                                 $error_counter++;
    //                                 $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                                 $count++;
    //                                 continue;
    //                             }
    //                         $enr->active_status = '0';
    //                         $enr->save();
    //                         $reg = StudentRegistration::where('roll_no', $all_data[1])->first();
    //                         $reg->student_status = 'withdrawl';
    //                         $reg->save();
    //                          DB::commit();

    //                         // Proceed with withdrawal logic if no errors
    //                         // Add to successful records
    //                         $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
    //                         $success_counter++;
    //                         // widthdraw data 


    //                     // Check for empty data in first column
    //                     // if (empty($all_data[2])) {
    //                     //     $reason = 'Empty enrollment ID';
    //                     //     $error_counter++;
    //                     //     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                     //     $count++;
    //                     //     continue;
    //                     // }
    //                     // $reg = StudentRegistration::where('roll_no', $all_data[0])->first();

    //                     // if (!$reg) {
    //                     //     $reason = 'Registration not found';
    //                     //     $error_counter++;
    //                     //     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                     //     $count++;
    //                     //     continue;
    //                     // }
    //                     // // Check for existing enrollment to avoid duplication
    //                     // $existingEnrollment = StudentEnrollments::where('regId', $reg->id)->first();
    //                     // $class = Classes::where('name', 'like', '%' . $all_data[5] . '%')->where('owned_by', '6')->first();
    //                     // if ($class) {
    //                     //     $section = Section::whereRaw('LOWER(name) like ?', ['%' . strtolower($all_data[6]) . '%'])->first();
    //                     // }
    //                     // // dd($all_data,$class, $section);
    //                     // $reg->dob = date('Y-m-d', strtotime($all_data[7]));
    //                     // $reg->class_id = $class->id;
    //                     // $reg->student_status = 'Enrolled';
    //                     // $reg->save();
    //                     //     $enrollment_date = date('Y-m-d', strtotime($all_data[1]));
    //                     // if ($existingEnrollment) {
    //                     //         $existingEnrollment->class_id = $class->id;
    //                     //         $existingEnrollment->section_id = $section->id;
    //                     //         $existingEnrollment->owned_by = 6;
    //                     //         $existingEnrollment->active_status = 1;
    //                     //         // $existingEnrollment->adm_date = $enrollment_date;
    //                     //         $existingEnrollment->save();
    //                     //     // if ($existingEnrollment->enrollId != '' && $existingEnrollment->enrollId != null) {
    //                     //     //     $existingEnrollment->enrollId = $all_data[2];
    //                     //     //     $existingEnrollment->save();
    //                     //     // }
    //                     //     // $reason = 'Student already enrolled in this session';
    //                     //     // $duplication_counter++;
    //                     //     // $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                     //     // $count++;
    //                     //     // continue;
    //                     // }else{

    //                     //     // Create new enrollment
    //                     //     $newEnrollId = $all_data[0];
    //                     //     $created_at = date('Y-m-d H:i:s', strtotime($all_data[2]));

    //                     //     $enrollment = new StudentEnrollments();
    //                     //     $enrollment->enrollId = $newEnrollId;
    //                     //     $enrollment->regId = $reg->id;
    //                     //     $enrollment->adm_date = $enrollment_date;
    //                     //     $enrollment->class_id = $class->id;
    //                     //     $enrollment->section_id = $section->id;
    //                     //     $enrollment->session_id =2;
    //                     //     $enrollment->owned_by = 6;
    //                     //     $enrollment->active_status = 1;
    //                     //     $enrollment->created_by = \Auth::user()->creatorId();
    //                     //     $enrollment->created_at = $created_at;
    //                     //     $enrollment->updated_at = $created_at;
    //                     //     $enrollment->save();
    //                     //     $reg->roll_no = $newEnrollId;
    //                     //     $reg->save();
    //                     // }
    //                     // Add to successful records
    //                     $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
    //                     $success_counter++;
    //                 } else {
    //                     // Save the header row with additional columns
    //                     $header = $all_data;
    //                     $header[] = 'Status';
    //                     $header[] = 'Reason';
    //                     $processed_records[] = $header;
    //                 }
    //                 $count++;
    //             }
    //             fclose($handle);
    //             DB::commit();
    //             if (!empty($skip_data)) {
    //                 $export_filename = 'enrollment_errors_' . time() . '.csv';
    //                 $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
    //                 $error_file = fopen($error_filepath, 'w+');
    //                 fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
    //                 foreach ($skip_data as $row) {
    //                     fputcsv($error_file, $row);
    //                 }
    //                 fclose($error_file);
    //                 return response()->download($error_filepath)->deleteFileAfterSend(true);
    //             }
    //             return redirect()->back()->with('message', "{$success_counter} Enrollment(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
    //         }
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         dd($e,$all_data);
    //         return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     }
    //     // set_time_limit(0);
    //     // $file = $request->file('excel_file');
    //     // $filename = $file->getClientOriginalName();
    //     // $file->move(public_path('assets/import/csv_file/'), $filename);
    //     // $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // // Initialize counters and tracking arrays
    //     // $success_counter = 0;
    //     // $error_counter = 0;
    //     // $duplication_counter = 0;
    //     // $skip_data = [];
    //     // $processed_records = [];

    //     // DB::beginTransaction();
    //     // try {
    //     //     if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //     //         $count = 0;

    //     //         while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
    //     //             if ($count > 0) {
    //     //                 $record_status = 'Error';
    //     //                 $reason = '';
    //     //                 // Check for empty data in first column
    //     //                 if (empty($all_data[2])) {
    //     //                     $reason = 'Empty enrollment ID';
    //     //                     $error_counter++;
    //     //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //     //                     $count++;
    //     //                     continue;
    //     //                 }
    //     //                 $reg = StudentRegistration::where('stdname', 'like', '%' . $all_data[3] . '%')
    //     //                     ->Where('fathername', 'like', '%' . $all_data[4] . '%')->first();
    //     //                 if (!$reg) {
    //     //                     $reason = 'Registration not found';
    //     //                     $error_counter++;
    //     //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //     //                     $count++;
    //     //                     continue;
    //     //                 }
    //     //                 // Check for existing enrollment to avoid duplication
    //     //                 $existingEnrollment = StudentEnrollments::where('regId', $reg->id)
    //     //                     ->first();

    //     //                 if ($existingEnrollment) {
    //     //                     if ($existingEnrollment->enrollId != '' && $existingEnrollment->enrollId != null) {
    //     //                         $existingEnrollment->enrollId = $all_data[2];
    //     //                         $existingEnrollment->save();
    //     //                     }
    //     //                     $reason = 'Student already enrolled in this session';
    //     //                     $duplication_counter++;
    //     //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //     //                     $count++;
    //     //                     continue;
    //     //                 }

    //     //                 // Proceed with enrollment logic if no errors
    //     //                 $enrollment_date = date('Y-m-d', strtotime($all_data[2]));
    //     //                 $branch = User::where('name', 'like', '%' . $all_data[1] . '%')->first();
    //     //                 if (!$branch) {
    //     //                     $reason = 'Branch not found: ' . $all_data[1];
    //     //                     $error_counter++;
    //     //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //     //                     $count++;
    //     //                     continue;
    //     //                 }
    //     //                 $class = Classes::where('name', 'like', '%' . $all_data[8] . '%')->where('owned_by', $branch->id)->first();
    //     //                 if (!$class) {
    //     //                     $reason = 'Class not found: ' . $all_data[8] . ' for branch: ' . $branch->name;
    //     //                     $error_counter++;
    //     //                     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //     //                     $count++;
    //     //                     continue;
    //     //                 }
    //     //                 $section = Section::where('name', 'like', '%' . $all_data[9] . '%')
    //     //                     ->where('owned_by', $branch->id)->first();
    //     //                 if (!$section) {
    //     //                     //create section
    //     //                     $section = new Section();
    //     //                     $section->name = $all_data[9];
    //     //                     $section->created_by = auth()->user()->id;
    //     //                     $section->owned_by = $branch->id;
    //     //                     $section->save();
    //     //                 }

    //     //                 // Create new enrollment
    //     //                 $newEnrollId = $all_data[0];
    //     //                 $created_at = date('Y-m-d H:i:s', strtotime($all_data[2]));

    //     //                 $enrollment = new StudentEnrollments();
    //     //                 $enrollment->enrollId = $newEnrollId;
    //     //                 $enrollment->regId = $reg->id;
    //     //                 $enrollment->adm_date = $enrollment_date;
    //     //                 $enrollment->class_id = $class->id;
    //     //                 $enrollment->section_id = $section->id;
    //     //                 $enrollment->session_id = $branch->id;
    //     //                 $enrollment->owned_by = $branch->id;
    //     //                 $enrollment->created_by = \Auth::user()->creatorId();
    //     //                 $enrollment->created_at = $created_at;
    //     //                 $enrollment->updated_at = $created_at;
    //     //                 $enrollment->save();
    //     //                 $reg->roll_no = $newEnrollId;
    //     //                 $reg->save();
    //     //                 // Add to successful records
    //     //                 $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
    //     //                 $success_counter++;
    //     //             } else {
    //     //                 // Save the header row with additional columns
    //     //                 $header = $all_data;
    //     //                 $header[] = 'Status';
    //     //                 $header[] = 'Reason';
    //     //                 $processed_records[] = $header;
    //     //             }
    //     //             $count++;
    //     //         }
    //     //         fclose($handle);
    //     //         DB::commit();
    //     //         if (!empty($skip_data)) {
    //     //             $export_filename = 'enrollment_errors_' . time() . '.csv';
    //     //             $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
    //     //             $error_file = fopen($error_filepath, 'w+');
    //     //             fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
    //     //             foreach ($skip_data as $row) {
    //     //                 fputcsv($error_file, $row);
    //     //             }
    //     //             fclose($error_file);
    //     //             return response()->download($error_filepath)->deleteFileAfterSend(true);
    //     //         }
    //     //         return redirect()->back()->with('message', "{$success_counter} Enrollment(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
    //     //     }
    //     // } catch (\Exception $e) {
    //     //     DB::rollBack();
    //     //     dd($e);
    //     //     return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     // }
    // }
    private function EnrollmentStatus($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';
                        // dd($all_data);
                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $reason = 'Empty enrollment ID';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
                        if (!$enr) {
                            $reason = 'Enrollment not found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $branch_name = iconv('UTF-8', 'UTF-8//IGNORE', string: (string) $all_data[3]);
                        $branch_name = trim(preg_replace('/\s+/', ' ', $branch_name));
                        $branch = User::where('name', 'like', '%' . $branch_name . '%')->first();
                        if (!$branch) {
                            dd($all_data, 'branch');
                            $reason = 'Branch not found: ' . $all_data[3];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $q = Classes::where('name', 'like', '%' . $all_data[1] . '%')->where('owned_by', $branch->id);
                        $class = $q->first();
                        if (!$class) {
                            dd($all_data, 'class', $enr, $q->toSql(), $q->getBindings(), $branch, $branch_name);
                            $reason = 'Class not found: ' . $all_data[1];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $section = Section::where('name', 'like', '%' . $all_data[2] . '%')->first();
                        if (!$section) {
                            dd($all_data, 'section');
                            $reason = 'Section not found: ' . $all_data[2];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $enr->class_id = $class->id;
                        $enr->section_id = $section->id;
                        $enr->adm_branch = $branch->id;
                        $enr->owned_by = $branch->id;
                        $enr->save();
                        $reg = StudentRegistration::where('roll_no', $all_data[0])->where('reg_no', $enr->regId)->where('owned_by', $enr->owned_by)->first();
                        if (!$reg) {
                            $reg = StudentRegistration::where('roll_no', $all_data[0])->first();
                            if ($reg) {
                                $reg->reg_no = $enr->regId;
                                $reg->save();
                            } else {
                                dd($all_data, 'reg');
                                $reason = 'Registration not found';
                                $error_counter++;
                                $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                                $count++;
                                continue;
                            }
                        }
                        $reg->reg_class = $class->id;
                        $reg->class_id = $class->id;
                        $reg->branch = $branch->id;
                        $reg->owned_by = $branch->id;
                        $reg->save();
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'enrollment_status_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Enrollment(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function RegistrationImport2($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';
                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $reason = 'Empty registration number';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Validate registration
                        $reg = $all_data[0];
                        if (!$reg) {
                            $reason = 'Registration Number not found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $branch = User::where('name', 'like', '%' . $all_data[32] . '%')->first();
                        if (!$branch) {
                            $reason = 'Branch not found: ' . $all_data[32];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $class = Classes::where('name', 'like', '%' . $all_data[27] . '%')->where('owned_by', $branch->id)->first();
                        if (!$class) {
                            dd($branch, $all_data, $class, $all_data[32]);
                            $reason = 'Class not found: ' . $all_data[31];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        // Check for existing registration to avoid duplication
                        $existingRegistration = StudentRegistration::where('reg_no', $all_data[0])
                            ->first();

                        if ($existingRegistration) {
                            $reason = 'Registration already exists for this session';
                            $duplication_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Proceed with registration logic if no errors
                        $regdate = date('Y-m-d', strtotime($all_data[1]));
                        $created_at = date('Y-m-d H:i:s', strtotime($all_data[1]));
                        $parts = preg_split("/\s*s\/d\/o\s*/i", $all_data[2]);


                        // Update registration
                        $reg = new StudentRegistration();
                        $reg->regdate = $regdate;
                        $reg->stdname = trim($parts[0]);
                        $reg->fathername = trim($parts[1]);
                        $reg->fatherphone = $all_data[9] ?? '';
                        $reg->fathercell = $all_data[9] ?? '';
                        $reg->reg_no = $all_data[0] ?? '';
                        $reg->branch = $branch->id ?? '';
                        $reg->class_id = $class->id ?? '';
                        $reg->reg_class = $class->id ?? '';
                        $reg->session_id = 2;
                        $reg->registrationfee = $all_data[30] ?? 0;
                        $reg->fatherprofession = $all_data[11] ?? '';
                        $reg->mothername = $all_data[12] ?? '';
                        $reg->motherprofession = $all_data[14] ?? '';
                        $reg->dob = date('Y-m-d', strtotime($all_data[3])) ?? '';
                        $reg->gender = $all_data[5] ?? '';
                        $reg->register_option = $all_data[30] == '1500' ? 2 : 1;
                        $reg->city = $all_data[22] ?? '';
                        $reg->district = $all_data[23] ?? '';
                        $reg->address = $all_data[24] ?? '';
                        $reg->permanent_address = $all_data[24] ?? '';
                        $reg->owned_by = $branch->id ?? '';
                        $reg->created_by = \Auth::user()->creatorId();
                        $reg->save();
                        $reg->created_at = $created_at;
                        $reg->updated_at = $created_at;
                        $reg->save();

                        if ($reg) {
                            //challan 
                            $challan = new Challans();
                            $challan->student_id = $reg->id;
                            $challan->class_id = $reg->class_id ?? null;
                            $challan->rollno = $reg->id;
                            $challan->challanNo = $this->challanNo();
                            $challan->challan_date = $regdate;
                            $challan->challan_type = 'Registration';
                            $challan->total_amount = $all_data[30] ?? 0;
                            $challan->paid_amount = $all_data[30] ?? 0;
                            $challan->issue_date = $regdate;
                            $challan->due_date = date('Y-m-d', strtotime($regdate . ' +7 days'));
                            $challan->status = 'Paid';
                            $challan->session_id = $reg->session_id;
                            $challan->owned_by = $reg->owned_by;
                            $challan->created_by = \Auth::user()->creatorId();
                            $challan->created_at = $created_at;
                            $challan->updated_at = $created_at;
                            $challan->save();
                            if ($challan) {
                                $reg_dis = Registring_option::where('id', $all_data[30] == '1500' ? 2 : 1)
                                    ->where('created_by', \Auth::user()->creatorId())
                                    ->first();
                                // Handle due date (one week after registration date)
                                try {
                                    $due_date = Carbon::parse($regdate)->addWeek()->toDateString();
                                } catch (\Exception $e) {
                                    $due_date = Carbon::now()->addWeek()->toDateString();
                                }

                                $challan->due_date = $due_date;
                                $challan->status = 'Paid';
                                $challan->session_id = $reg->session_id;
                                $challan->owned_by = $reg->owned_by;
                                $challan->created_by = \Auth::user()->creatorId();
                                $challan->save();
                                $challan->created_at = $created_at;
                                $challan->updated_at = $created_at;
                                $challan->save();
                                // Find bank account
                                $bankAccount = BankAccount::where('owned_by', $challan->owned_by)
                                    ->where('bank_name', 'like', '%CSH')->first();
                                if (!$bankAccount) {
                                    $error_reason = 'Bank account not found for owner ID: ' . $challan->owned_by;
                                    $error_counter++;
                                    $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                                    $skipped_records[] = $all_data; // Store the
                                    $count++;
                                    continue;
                                }

                                // Create student receipt
                                $recipts = new StudentReceipt();
                                $recipts->recipt_date = $regdate;
                                $recipts->challan_id = $challan->id;
                                $recipts->recipt_amount = $challan->paid_amount;
                                $recipts->student_id = $challan->student_id;
                                $recipts->challan_amount = $challan->paid_amount;
                                $recipts->late_amount = 0;
                                $recipts->arrears = 0;
                                $recipts->bank_id = $bankAccount->id;
                                $recipts->referance = 'Registration Fee';
                                $recipts->receive_type = 'CD';
                                $recipts->received_by = Auth::user()->id;
                                $recipts->owned_by = $challan->owned_by;
                                $recipts->created_by = \Auth::user()->creatorId();
                                $recipts->save();
                                $recipts->created_at = $created_at;
                                $recipts->updated_at = $created_at;
                                $recipts->save();
                                // Find registration fee head
                                $pattern = '%registration%';
                                $adm_fee_head = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($pattern)])->first();
                                if (!$adm_fee_head) {
                                    $error_reason = 'Registration fee head not found';
                                    $error_counter++;
                                    $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                                    $skipped_records[] = $all_data; // Store the
                                    $count++;
                                    continue;
                                    // throw new \Exception('Registration fee head not found');
                                }

                                // Create challan head
                                $challan_head = new ChallanHead();
                                $challan_head->challan_id = $challan->id;
                                $challan_head->head_id = $adm_fee_head->id;
                                $challan_head->price = $reg_dis->discount ?? 0;
                                $challan_head->concession = 0;
                                $challan_head->save();
                                $challan_head->created_at = $created_at;
                                $challan_head->updated_at = $created_at;
                                $challan_head->save();

                                $item['0']['head'] = $challan_head->head_id;
                                $item['0']['price'] = $reg_dis->discount ?? 0;
                                $item['0']['quantity'] = 1;
                                $item['0']['concession'] = 0;
                                $item['0']['total'] = $reg_dis->discount ?? 0;

                                $data = [
                                    'id' => $challan->id,
                                    'no' => $challan->challanNo,
                                    'date' => $challan->challan_date,
                                    'recipt' => $recipts->id,
                                    'reference' => $challan->student_id,
                                    'description' => 'Registration Fee Amount',
                                    'user_id' => $challan->student_id,
                                    'amount' => $challan->paid_amount,
                                    'total' => $challan->paid_amount,
                                    'user_type' => 'Student',
                                    'category' => 'Registration',
                                    'owned_by' => $challan->owned_by,
                                    'created_by' => $challan->created_by,
                                    'account_id' => $bankAccount->chart_account_id,
                                    'items' => $item,
                                    'created_at' => $created_at
                                ];

                                // Update registration fee
                                $updateStdreg = StudentRegistration::find($reg->id);
                                if ($updateStdreg) {
                                    $updateStdreg->registrationfee = $reg_dis->discount ?? 0;
                                    $updateStdreg->created_at = $created_at;
                                    $updateStdreg->save();
                                }

                                // Update bank account balance
                                if ($bankAccount->id) {
                                    Utility::bankAccountBalance($bankAccount->id, $challan->paid_amount, 'credit');
                                }

                                // Create accounting entries
                                $dataret = Utility::crv_entry($data);
                                $challan->voucher_id = $dataret;
                                $challan->save();
                            }
                        }

                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'registration_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Registration(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }



    // private function WithdrawImport($file, $request)
    // {
    //     set_time_limit(0);
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // Initialize counters and tracking arrays
    //     $success_counter = 0;
    //     $error_counter = 0;
    //     $duplication_counter = 0;
    //     $skip_data = [];
    //     $processed_records = [];

    //     DB::beginTransaction();
    //     try {
    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             $count = 0;

    //             while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
    //                 if ($count > 0) {
    //                     $record_status = 'Error';
    //                     $reason = '';

    //                     // Check for empty data in first column
    //                     if (empty($all_data[0])) {
    //                         $reason = 'Empty enrollment ID';
    //                         $error_counter++;
    //                     } else {
    //                         $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[4]);
    //                         $branchfrom = User::where('name', $clean_title)->first();
    //                         if (!$branchfrom) {
    //                             dd($all_data, 'branchfrom', $clean_title);
    //                             $reason = 'From branch not found: ' . $all_data[4];
    //                             $error_counter++;
    //                         } else {
    //                             // Validate enrollment
    //                             $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
    //                             if (!$enr) {
    //                                 $reason = 'Enrollment not found: ' . $all_data[0];
    //                                 $error_counter++;
    //                             } else {
    //                                 // Check for existing withdrawal (prevent duplicates)
    //                                 $withdraw = StudentWithdrawal::where('student_id', $enr->enrollId)->first();
    //                                 if ($withdraw) {
    //                                     $reason = 'Student already withdrawn';
    //                                     $duplication_counter++;
    //                                 }
    //                             }
    //                         }
    //                     }

    //                     // Log the error or skip
    //                     if ($reason) {
    //                         $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                         $processed_records[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
    //                     } else {
    //                         // Proceed with withdrawal logic if no errors
    //                         $withdrawal_date = date('Y-m-d', strtotime($all_data[1]));

    //                         // Create new withdrawal
    //                         $withdrawal = new StudentWithdrawal();
    //                         $withdrawal->student_id = $enr->enrollId;
    //                         $withdrawal->challan_id = null;
    //                         $withdrawal->branch_id = $branchfrom->id;
    //                         $withdrawal->class_id = $enr->class_id;
    //                         $withdrawal->withdraw_date = $withdrawal_date;
    //                         $withdrawal->apply_date = $withdrawal_date;
    //                         $withdrawal->reason = $all_data[3] ?? $all_data[2]; // Modify accordingly
    //                         $withdrawal->remark = $all_data[3] ?? $all_data[2]; // Modify accordingly
    //                         $withdrawal->owned_by = $enr->owned_by;
    //                         $withdrawal->created_by = \Auth::user()->creatorId();
    //                         $withdrawal->save();

    //                         // Add to successful records
    //                         $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
    //                         $success_counter++;
    //                     }
    //                 } else {
    //                     // Save the header row with additional columns
    //                     $header = $all_data;
    //                     $header[] = 'Status';
    //                     $header[] = 'Reason';
    //                     $processed_records[] = $header;
    //                 }
    //                 $count++;
    //             }
    //             fclose($handle);
    //             DB::commit();

    //             // Generate error file if there are errors
    //             if (!empty($skip_data)) {
    //                 $export_filename = 'withdraw_errors_' . time() . '.csv';
    //                 $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
    //                 $error_file = fopen($error_filepath, 'w+');

    //                 // Write the header for the error file
    //                 fputcsv($error_file, ['Enrollment ID', 'Status', 'Reason']);

    //                 foreach ($skip_data as $row) {
    //                     fputcsv($error_file, $row);
    //                 }

    //                 fclose($error_file);
    //                 return response()->download($error_filepath)->deleteFileAfterSend(true);
    //             }

    //             // If no errors, create a success report
    //             $export_filename = 'withdrawal_results_' . time() . '.csv';
    //             return response()->download(public_path('assets/import/csv_file/' . $export_filename))
    //                 ->with('message', "{$success_counter} Student Withdrawal(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
    //         }
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         dd($e);
    //         return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     }
    // }

    private function WithdrawImport($file, $request)
    {

        set_time_limit(0);
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';
                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $reason = 'Empty enrollment ID';
                            $error_counter++;
                        } else {
                            $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[4]);
                            // $branchfrom = User::where('name', $clean_title)->first();
                            // if (!$branchfrom) {
                            //     dd($all_data, 'branchfrom', $clean_title);
                            //     $reason = 'From branch not found: ' . $all_data[4];
                            //     $error_counter++;
                            // } else {
                            // Validate enrollment
                            $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
                            if (!$enr) {
                                $reason = 'Enrollment not found: ' . $all_data[0];
                                $error_counter++;
                            } else {
                                // Check for existing withdrawal (prevent duplicates)
                                $withdraw = StudentWithdrawal::where('student_id', $enr->regId)->first();
                                if ($withdraw) {
                                    $reason = 'Student already withdrawn';
                                    $duplication_counter++;
                                }
                            }
                            // }
                        }

                        // Log the error or skip
                        if ($reason) {
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $processed_records[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        } else {
                            // Proceed with withdrawal logic if no errors
                            $created_at = date('Y-m-d H:i:s', strtotime($all_data[1]));
                            $withdrawal_date = date('Y-m-d', strtotime($all_data[1]));
                            $reg = StudentRegistration::where('roll_no', $enr->enrollId)->first();
                            if (!$reg) {
                                dd('asd', $all_data);
                            }
                            $class = Classes::where('name', 'like', '%' . $all_data[7] . '%')->where('owned_by', $reg->branch)->first();
                            if (!$class) {
                                dd('asdas');
                                $reason = 'Class not found: ' . $all_data[7];
                                $error_counter++;
                                $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                                $count++;
                                continue;
                            }
                            // Create new withdrawal
                            $withdrawal = new StudentWithdrawal();
                            $withdrawal->student_id = $reg->id;
                            $withdrawal->challan_id = null;
                            $withdrawal->branch_id = $reg->branch;
                            $withdrawal->class_id = $class->id;
                            $withdrawal->withdraw_date = $withdrawal_date;
                            $withdrawal->apply_date = $withdrawal_date;
                            $withdrawal->reason = $all_data[3] ?? $all_data[2]; // Modify accordingly
                            $withdrawal->remark = $all_data[3] ?? $all_data[2]; // Modify accordingly
                            $withdrawal->owned_by = $reg->owned_by;
                            $withdrawal->created_by = \Auth::user()->creatorId();
                            $withdrawal->save();
                            $withdrawal->created_at = $created_at;
                            $withdrawal->updated_at = $created_at;
                            $withdrawal->save();


                            $reg->active_status = 0;
                            $reg->student_status = 'withdrawal';
                            $reg->save();

                            $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
                            $enr->active_status = 0;
                            $enr->save();
                            // Add to successful records
                            $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                            $success_counter++;
                        }
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();

                // Generate error file if there are errors
                if (!empty($skip_data)) {
                    $export_filename = 'withdraw_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');

                    // Write the header for the error file
                    fputcsv($error_file, ['Enrollment ID', 'Status', 'Reason']);

                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }

                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                // If no errors, create a success report
                $export_filename = 'withdrawal_results_' . time() . '.csv';
                return response()->download(public_path('assets/import/csv_file/' . $export_filename))
                    ->with('message', "{$success_counter} Student Withdrawal(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
            dd('asd');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }

    private function StudentDetail($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize tracking arrays
        $processed_records = [];
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = []; // Array to gather records that were skipped

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 4000, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';

                        if (empty($all_data[0])) {
                            $error_counter++;
                            // Record the skip data (entire all_data)
                            $skip_data[] = $all_data;
                            continue;
                        }

                        $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
                        if (!$enr) {
                            $error_counter++;
                            // Record the skip data (entire all_data)
                            $skip_data[] = $all_data;
                            continue;
                        }

                        $reg = StudentRegistration::where('reg_no', $enr->regId)->where('owned_by', $enr->owned_by)->first();
                        if (!$reg) {
                            $error_counter++;
                            // Record the skip data (entire all_data)
                            $skip_data[] = $all_data;
                            continue;
                        }

                        $class = Classes::where('name', $all_data[1])->where('owned_by', $enr->owned_by)->first();
                        if (!$class) {
                            $error_counter++;
                            // Record the skip data (entire all_data)
                            $skip_data[] = $all_data;
                            continue;
                        }

                        $section = Section::where('name', $all_data[2])->first();
                        if (!$section) {
                            $error_counter++;
                            // Record the skip data (entire all_data)
                            $skip_data[] = $all_data;
                            continue;
                        }

                        $sectionclass = ClassSection::where('class_id', $class->id)
                            ->where('section_id', $section->id)
                            ->where('owned_by', $enr->owned_by)
                            ->where('active_status', 1)
                            ->first();

                        if (!$sectionclass) {
                            if ($enr->owned_by == 53) {
                                $sectionclass = new ClassSection();
                                $sectionclass->class_id = $class->id;
                                $sectionclass->section_id = $section->id;
                                $sectionclass->owned_by = $enr->owned_by;
                                $sectionclass->created_by = auth()->user()->id;
                                $sectionclass->save();
                            } else {
                                $error_counter++;
                                // Record the skip data (entire all_data)
                                $skip_data[] = $all_data;
                                continue;
                            }
                        }

                        // Updating registration data
                        if ($reg) {
                            $reg->mothername = $all_data[4];
                            $reg->fatherprofession = $all_data[5];
                            $reg->motherprofession = $all_data[6];
                            $reg->address = $all_data[7];
                            $reg->permanent_address = $all_data[7]; // Assuming it's in the same column
                            $reg->register_option = 1;
                            $reg->reg_class = $sectionclass->class_id;
                            $reg->class_id = $sectionclass->class_id;
                            $reg->save();
                        }

                        $enr->class_id = $sectionclass->class_id;
                        $enr->adm_session = 2;
                        $enr->section_id = $sectionclass->section_id;
                        $enr->save();

                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();

                // Generate error file if there are errors
                if (!empty($skip_data)) {
                    $export_filename = 'student_details_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');

                    // Write the error file header
                    fputcsv($error_file, ['Enrollment ID', 'Class', 'Section', 'Mother Name', 'Father Profession', 'Mother Profession', 'Address', 'Permanent Address']);

                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }

                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }

                // If no errors, generate a success report
                $export_filename = 'student_details_results_' . time() . '.csv';
                return response()->download(public_path('assets/import/csv_file/' . $export_filename))
                    ->with('success', "{$success_counter} Student Detail(s) added successfully. {$error_counter} rows skipped due to errors.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function EnrollmentImport($file, $request)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skipped_records = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;
                $header = null;

                while (($all_data = fgetcsv($handle, 4000, ",")) !== FALSE) {
                    if ($count === 0) {
                        // Save header row with additional columns for status info
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                        $count++;
                        continue;
                    }

                    // Skip empty rows
                    if (empty(array_filter($all_data))) {
                        $count++;
                        continue;
                    }

                    $all_data = array_map('trim', $all_data);
                    $record_status = 'Error';
                    $reason = '';

                    $registration = StudentRegistration::where('reg_no', $all_data[1])->first();

                    // Skip if registration doesn't exist
                    if ($registration == null) {
                        $reason = 'Registration not found';
                        $skipped_records[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        $processed_records[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        $error_counter++;
                        $count++;
                        continue;
                    }
                    // dd($all_data);
                    // Check for existing enrollment to avoid duplication
                    $existingEnrollment = StudentEnrollments::where('regId', $all_data[1])
                        ->where('enrollId', $all_data[11])
                        ->where('session_id', $registration->session_id)
                        ->first();

                    if ($existingEnrollment) {
                        // Check if this is a second installment for an existing enrollment
                        if (date('Y-m-d', strtotime($existingEnrollment->adm_date)) == date('Y-m-d', strtotime($all_data[4]))) {
                            $secondInstallmentChallan = Challans::where('student_id', $all_data[1])
                                ->where('rollno', $all_data[11])
                                ->where('session_id', $registration->session_id)
                                ->where('challan_type', 'Admission')
                                ->where('challan_date', date('Y-m-d', strtotime($all_data[8])))
                                ->first();
                            if (!$secondInstallmentChallan) {
                                $this->processAdmissionChallan($all_data, $registration, $all_data[11], date('Y-m-d H:i:s', strtotime($all_data[8])));
                                $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => 'Second installment challan created']);
                                $count++;
                                continue;
                            }
                        }
                        // dd('condition false');
                        $reason = 'Student already enrolled in this session';
                        $skipped_records[] = array_merge($all_data, ['Status' => 'Duplicate', 'Reason' => $reason]);
                        $processed_records[] = array_merge($all_data, ['Status' => 'Duplicate', 'Reason' => $reason]);
                        $duplication_counter++;
                        $count++;
                        continue;
                    }

                    // Check for existing admission challan to avoid duplication
                    $existingChallan = Challans::where('student_id', $all_data[1])
                        ->where('rollno', $all_data[11])
                        ->where('session_id', $registration->session_id)
                        ->where('challan_type', 'Admission')
                        ->first();

                    if ($existingChallan) {
                        $reason = 'Admission challan already exists for this student';
                        $skipped_records[] = array_merge($all_data, ['Status' => 'Duplicate', 'Reason' => $reason]);
                        $processed_records[] = array_merge($all_data, ['Status' => 'Duplicate', 'Reason' => $reason]);
                        $duplication_counter++;
                        $count++;
                        continue;
                    }

                    try {
                        // Get section once
                        $section = ClassSection::where('class_id', $registration->class_id)->first();

                        // Create enrollment
                        $newEnrollId = $all_data[11];
                        $created_at = date('Y-m-d H:i:s', strtotime($all_data[4]));

                        $enrollment = new StudentEnrollments();
                        $enrollment->enrollId = $newEnrollId;
                        $enrollment->regId = $all_data[1];
                        $enrollment->adm_date = date('Y-m-d', strtotime($all_data[4]));
                        $enrollment->class_id = $registration->class_id ?? NULL;
                        $enrollment->section_id = $section->id ?? NULL;
                        $enrollment->session_id = $registration->session_id;
                        $enrollment->owned_by = $registration->owned_by;
                        $enrollment->created_by = \Auth::user()->creatorId();
                        $enrollment->created_at = $created_at;
                        $enrollment->updated_at = $created_at;
                        $enrollment->save();

                        // Update registration status
                        $registration->roll_no = $newEnrollId;
                        $registration->student_status = 'Enrolled';
                        $registration->save();

                        // Process challan and fee heads
                        $this->processAdmissionChallan($all_data, $registration, $newEnrollId, $created_at);

                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } catch (\Exception $innerException) {
                        $reason = 'Processing error: ' . $innerException->getMessage();
                        $skipped_records[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        $processed_records[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        $error_counter++;
                    }

                    $count++;
                }
                fclose($handle);
                DB::commit();

                // Generate error file if there are errors
                if (!empty($skipped_records)) {
                    $export_filename = 'enrollment_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');

                    // Write the header for the error file
                    fputcsv($error_file, ['Enrollment ID', 'Status', 'Reason']); // Header can be modified as required

                    foreach ($skipped_records as $row) {
                        fputcsv($error_file, $row);
                    }

                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                $export_filename = 'enrollment_results_' . time() . '.csv';
                return response()->download(public_path('assets/import/csv_file/' . $export_filename))
                    ->with('success', "Import completed. Processed: {$count}, Success: {$success_counter}, Duplicates: {$duplication_counter}, Errors: {$error_counter}");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function processAdmissionChallan($all_data, $registration, $newEnrollId, $created_at)
    {
        $admission_fee = !empty($all_data[5]) ? floatval($all_data[5]) : 0;
        $security_fee = !empty($all_data[6]) ? floatval($all_data[6]) : 0;
        $annual_fee = !empty($all_data[7]) ? floatval($all_data[7]) : 0;
        $other_fee = !empty($all_data[9]) ? floatval($all_data[9]) : 0; // For now it's set to 0

        $total_amount = $admission_fee + $security_fee + $annual_fee + $other_fee;

        // Create challan
        $challan = new Challans();
        $challan->student_id = $all_data[1];
        $challan->class_id = $registration->class_id ?? null;
        $challan->rollno = $newEnrollId;
        $challan->challanNo = $this->challanNo();
        $challan->challan_date = date('Y-m-d', strtotime($all_data[8]));
        $challan->fee_month = date('Y-m-d', strtotime($all_data[8]));
        $challan->challan_type = 'Admission';
        $challan->total_amount = $total_amount;
        $challan->paid_amount = 0;
        $challan->issue_date = date('Y-m-d', strtotime($all_data[8]));
        $challan->due_date = date('Y-m-d', strtotime($all_data[8]));
        $challan->status = 'unpaid';
        $challan->session_id = $registration->session_id;
        $challan->owned_by = $registration->owned_by;
        $challan->created_by = \Auth::user()->creatorId();
        $challan->created_at = $created_at;
        $challan->updated_at = $created_at;
        $challan->save();

        $items = [];
        $item_index = 0;

        // Process fee heads
        $fee_heads = [
            ['amount' => $admission_fee, 'pattern' => '%admission%'],
            ['amount' => $security_fee, 'pattern' => '%security%'],
            ['amount' => $annual_fee, 'pattern' => '%annual%'],
            ['amount' => $other_fee, 'pattern' => '%tuition%']
        ];

        foreach ($fee_heads as $fee) {
            if ($fee['amount'] > 0) {
                // Find fee head
                $fee_head = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($fee['pattern'])])->first();
                if (!$fee_head) {
                    dd('Fee head not found for pattern: ' . $fee['pattern']);
                    // throw new \Exception('Fee head not found for pattern: ' . $fee['pattern']);
                }

                // Create challan head
                $challan_head = new ChallanHead();
                $challan_head->challan_id = $challan->id;
                $challan_head->head_id = $fee_head->id;
                $challan_head->price = $fee['amount'];
                $challan_head->concession = 0;
                $challan_head->created_at = $created_at;
                $challan_head->updated_at = $created_at;
                $challan_head->save();

                // Add to items array for journal entry
                $items[$item_index] = [
                    'head' => $fee_head->id,
                    'price' => $fee['amount'],
                    'quantity' => 1,
                    'concession' => 0,
                    'total' => $fee['amount']
                ];
                $item_index++;
            }
        }

        // Prepare data for journal entry
        $data = [
            'id' => $challan->id,
            'no' => $challan->challanNo,
            'date' => $challan->challan_date,
            'reference' => $challan->student_id,
            'description' => 'Admission Fee Amount',
            'user_id' => $challan->student_id,
            'amount' => $challan->paid_amount,
            'total' => $challan->paid_amount,
            'user_type' => 'Student',
            'category' => 'Admission',
            'owned_by' => $challan->owned_by,
            'created_by' => $challan->created_by,
            'items' => $items,
            'created_at' => $created_at
        ];

        // Create accounting entries
        $dataret = Utility::jrentry($data);
        $challan->voucher_id = $dataret;
        $challan->save();

        return $challan;
    }
    private function ClassImport(Request $data)
    {
        $file = $data->file('excel_file');
        if (!$file) {
            return redirect()->back()->with('error', 'No file uploaded.');
        }

        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Prepare error filename
        $error_filename = pathinfo($filename, PATHINFO_FILENAME) . '_errors_' . time() . '.csv';
        $error_filepath = public_path('assets/import/csv_file/' . $error_filename);

        $count = 0;
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $error_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) === FALSE) {
                return redirect()->back()->with('error', 'Could not open the file.');
            }

            while (($row = fgetcsv($handle, 1000, ",")) != FALSE) {
                if ($count == 0 || (isset($row[0]) && strtolower(trim($row[0])) == 'branch')) {
                    $count++;
                    continue;
                }
                $classes = Classes::where('name', 'like', $row[3] . '%')->where('owned_by', 6)->first();
                $section = Section::where('name', 'like', '%' . $row[5] . '%')->first();
                $classesold = Classes::where('name', 'like', $row[2] . '%')->where('owned_by', 6)->first();
                $sectionold = Section::where('name', 'like', $row[4] . '%')->first();
                if (!$classes) {
                    dd('sa', $row);
                }
                if (!$section) {
                    dd('na', $row);
                }
                $classsection = ClassSection::where('class_id', $classes->id)
                    ->where('section_id', $section->id)
                    ->where('owned_by', 6)
                    ->first();
                if (!$classsection) {

                } else {
                    $registration = StudentRegistration::where('roll_no', $row[0])->first();
                    $enrollment = StudentEnrollments::where('enrollId', $row[0])->first();
                    // dd($enrollment,$classes,$section,$classesold,$sectionold);
                    if ($enrollment) {
                        $enrollment->class_id = $classes->id;
                        $enrollment->section_id = $section->id;
                        $enrollment->save();
                    }
                    if ($registration) {
                        $registration->class_id = $classes->id;
                        $registration->save();
                    }
                }
                // $branch_name = iconv('UTF-8', 'UTF-8//IGNORE', (string) $row[2]);
                // $branch_name = trim(preg_replace('/\s+/', ' ', $branch_name));
                // $class_name = (string) $row[0];
                // $section_name = (string) $row[1];

                // if (empty($branch_name) || empty($class_name) || empty($section_name)) {
                //     $error_counter++;
                //     $error_records[] = [$branch_name, $class_name, $section_name, 'Incomplete data'];
                //     continue;
                // }
                // $branch = User::where('name', 'like', '%' . $branch_name . '%')->first();
                // if (!$branch) {
                //     dd($branch_name);
                //     $error_counter++;
                //     $error_records[] = [$branch_name, $class_name, $section_name, 'Branch not found'];
                //     continue;
                // }

                // $section = Section::where('name', $section_name)->where('active_status', 1)->first();
                // if (!$section) {
                //     $error_counter++;
                //     $error_records[] = [$branch_name, $class_name, $section_name, 'Section not found'];
                //     continue;
                // }

                // $class = Classes::where('name', $class_name)->where('owned_by', $branch->id)->first();
                // if ($class) {
                //     $class->active_status = 1;
                //     $class->save();
                //     $classsec = ClassSection::where('class_id', $class->id)
                //         ->where('section_id', $section->id)
                //         ->where('owned_by', $branch->id)
                //         ->first();
                //     // dd($class, $classsec);
                //     if ($classsec) {
                //         $classsec->active_status = 1;
                //         $classsec->section_id = $section->id;
                //         $classsec->class_id = $class->id;
                //         $classsec->owned_by = $branch->id;
                //         $classsec->save();
                //     } else {
                //         $classsec = ClassSection::create([
                //             'active_status' => 1,
                //             'class_id' => $class->id,
                //             'section_id' => $section->id,
                //             'owned_by' => $branch->id,
                //             'created_by' => 2,
                //         ]);
                //     }
                //     // $error_counter++;
                //     // $error_records[] = [$branch_name, $class_name, $section_name, 'Duplicat'];
                //     // continue;
                // } else {
                //     // dd($branch_name,$class_name,$section_name,$branch->id,$class);
                //     $class = new Classes();
                //     $class->name = $class_name;
                //     $class->owned_by = $branch->id;
                //     $class->created_by = auth()->user()->id;
                //     $class->save();
                // }
                // $exists = ClassSection::where('class_id', $class->id)
                //     ->where('section_id', $section->id)
                //     ->where('owned_by', $branch->id)
                //     ->exists();

                // if ($exists) {

                //     $error_counter++;
                //     $duplication_counter++;
                //     $error_records[] = [$branch_name, $class_name, $section_name, 'Class-Section combination already exists'];
                //     continue;
                // }

                // ClassSection::create([
                //     'active_status' => 1,
                //     'class_id' => $class->id,
                //     'section_id' => $section->id,
                //     'owned_by' => $branch->id,
                //     'created_by' => 2,
                // ]);

                $success_counter++;
                $count++;
            }

            fclose($handle);
            unlink($filepath);
            DB::commit();

            // If errors, create file and download it
            if ($error_counter > 0 && !empty($error_records)) {
                $error_file = fopen($error_filepath, 'w+');
                fputcsv($error_file, ['Branch', 'Class Name', 'Section', 'Error']);
                foreach ($error_records as $error_row) {
                    fputcsv($error_file, $error_row);
                }
                fclose($error_file);

                return response()->download($error_filepath)->deleteFileAfterSend(true);
            }

            return redirect()->back()->with('success', "{$success_counter} class-section combinations added successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            dd($e);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }

    // private function SectionImport($data)
    // {
    //     set_time_limit(0);
    //     $file = $data->file('excel_file');
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // Setup counters
    //     $count = 0;
    //     $success_counter = 0;
    //     $error_counter = 0;
    //     $duplication_counter = 0;
    //     $error_records = [];

    //     DB::beginTransaction();
    //     try {
    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             while (($all_data = fgetcsv($handle, 1000, ",")) !== FALSE) {
    //                 // Skip header
    //                 if ($count === 0 || (isset($all_data[0]) && strtolower(trim($all_data[0])) === 'section')) {
    //                     $count++;
    //                     continue;
    //                 }

    //                 $section_name = trim($all_data[0] ?? '');

    //                 if (empty($section_name)) {
    //                     $error_counter++;
    //                     $error_records[] = ['', '', $section_name, 'Section name is empty'];
    //                     continue;
    //                 }


    //                 $duplicate_check = Section::where('name', $section_name)->where('active_status', 1)->exists();
    //                 if ($duplicate_check) {
    //                     $error_counter++;
    //                     $duplication_counter++;
    //                     $error_records[] = [$branch->name ?? '', '', $section_name, 'Section already exists'];
    //                     continue;
    //                 }
    //                 Section::create([
    //                     'name' => $section_name,
    //                     'created_by' => 2,
    //                     'active_status' => 1,
    //                     'owned_by' => 2,
    //                 ]);
    //                 $success_counter++;
    //                 $count++;
    //             }
    //             fclose($handle);
    //             unlink($filepath);
    //         }

    //         DB::commit();

    //         // If errors occurred, generate downloadable error file
    //         if (!empty($error_records)) {
    //             $export_filename = 'section_import_errors_' . time() . '.csv';
    //             $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
    //             $error_file = fopen($error_filepath, 'w+');

    //             fputcsv($error_file, ['Branch', 'Class Name', 'Section', 'Error']);
    //             foreach ($error_records as $row) {
    //                 fputcsv($error_file, $row);
    //             }
    //             fclose($error_file);

    //             return response()->download($error_filepath)->deleteFileAfterSend(true);
    //         }

    //         return redirect()->back()->with('message', "{$success_counter} Section(s) added successfully. {$error_counter} rows skipped due to duplication or missing data.");
    //     } catch (\Exception $e) {
    //         DB::rollBack();

    //         if (file_exists($filepath)) {
    //             unlink($filepath);
    //         }
    //         dd($e);
    //         return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     }
    // }

    private function SectionImport($data)
    {
        set_time_limit(0);
        $file = $data->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Setup counters
        $count = 0;
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $error_records = [];

        DB::beginTransaction();
        try {

            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                while (($all_data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    // Skip header

                    // all_data[0] => table id
                    // all_data[1] => reg_date
                    // all_data[2] => reg_no
                    // all_data[3] => roll_no
                    // all_data[4] => stdname
                    // all_data[5] => father_name

                    $reg = StudentRegistration::where('id', $all_data[0])->first();
                    if (!$reg) {
                        $error_counter++;
                        $error_records[] = [$all_data[0], '', '', 'Registration not found'];
                        continue;
                    }
                    $enr = StudentEnrollments::where('regId', $reg->id)->first();
                    if ($enr) {
                        $enr->delete();
                    }

                    $challan = Challans::where('student_id', $reg->id)->get();
                    // dd($challan,$reg);
                    if ($challan) {
                        foreach ($challan as $ch) {
                            $head = ChallanHead::where('challan_id', $ch->id)->delete();
                            $journal = JournalEntry::where('id', $ch->voucher_id)->delete();
                            $item = JournalItem::where('journal', $ch->voucher_id)->delete();
                            $recipts = StudentReceipt::where('challan_id', $ch->id)->delete();
                            $ch->delete();
                        }

                    }
                    $reg->delete();


                    $success_counter++;
                    $count++;
                }
                fclose($handle);
                unlink($filepath);
            }

            DB::commit();

            // If errors occurred, generate downloadable error file
            if (!empty($error_records)) {
                $export_filename = 'section_import_errors_' . time() . '.csv';
                $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                $error_file = fopen($error_filepath, 'w+');

                fputcsv($error_file, ['Branch', 'Class Name', 'Section', 'Error']);
                foreach ($error_records as $row) {
                    fputcsv($error_file, $row);
                }
                fclose($error_file);

                return response()->download($error_filepath)->deleteFileAfterSend(true);
            }

            return redirect()->back()->with('message', "{$success_counter} Section(s) added successfully. {$error_counter} rows skipped due to duplication or missing data.");
        } catch (\Exception $e) {
            DB::rollBack();

            if (file_exists($filepath)) {
                unlink($filepath);
            }
            dd($e);
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }

    // private function RegistrationImport($file, $request)
    // {
    //     set_time_limit(0);
    //     $file = $request->file('excel_file');
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // Initialize error records tracking
    //     $error_records = [];
    //     $error_records[] = ['Row', 'Registration No', 'Student Name', 'Reason']; // Header row
    //     $skipped_records = []; // Array to gather records that were skipped

    //     DB::beginTransaction();
    //     try {
    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             $count = 0;
    //             $success_counter = 0;
    //             $error_counter = 0;
    //             $duplication_counter = 0;
    //             while (($all_data = fgetcsv($handle, 4000, ",")) !== FALSE) {
    //                 if ($count > 0) {
    //                     $error_reason = null;

    //                     // Check for empty data in first column
    //                     if (empty($all_data[0])) {
    //                         $error_reason = 'Branch name is empty';
    //                         $error_counter++;
    //                         $error_records[] = [$count, $all_data[1] ?? 'N/A', $all_data[2] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the skipped data
    //                         $count++;
    //                         continue;
    //                     }
    //                     //0 branch
    //                     //2 regno
    //                     //4 name
    //                     //5 father name
    //                     //6 class
    //                     $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[0]);
    //                     $branch = User::where('name', $clean_title)->first();
    //                     if (!$branch) {
    //                         $error_reason = 'Branch not found: ' . $clean_title;
    //                         $error_counter++;
    //                         $error_records[] = [$count, $all_data[0] ?? 'N/A', $all_data[1] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the skipped data
    //                         $count++;
    //                         continue;
    //                     }

    //                     // Check if student ID exists
    //                     if (empty($all_data[2])) {
    //                         $error_reason = 'Registration number is empty';
    //                         $error_counter++;
    //                         $error_records[] = [$count, 'N/A', $all_data[2] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the skipped data
    //                         $count++;
    //                         continue;
    //                     }

    //                     // Check for duplicate student registration
    //                     $duplicate_user_check = StudentRegistration::where('reg_no', $all_data[2])->get();
    //                     if ($duplicate_user_check->count() > 0) {
    //                         $error_reason = 'Duplicate registration number';
    //                         $error_counter++;
    //                         $duplication_counter++;
    //                         $error_records[] = [$count, $all_data[2], $all_data[1] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the skipped data
    //                         $count++;
    //                         continue;
    //                     }

    //                     // Set status values
    //                     $act_status = isset($all_data[9]) && strtoupper($all_data[9]) == 'YES' ? 1 : 0;
    //                     $std_status = isset($all_data[9]) && strtoupper($all_data[9]) == 'YES' ? 'Enrolled' : 'Registered';

    //                     // Format date - assuming format is d/m/Y (1/4/2019 = 4 Jan 2019)
    //                     $regdate = $all_data[3] ?? null;
    //                     if (!$regdate) {
    //                         $error_reason = 'Registration date is missing';
    //                         $error_counter++;
    //                         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the skipped data
    //                         $count++;
    //                         continue;
    //                     }

    //                     try {
    //                         $timestamp = strtotime($regdate); // Convert string to timestamp
    //                         if ($timestamp === false) {
    //                             throw new \Exception("Invalid date format");
    //                         }
    //                         $regdate = date('Y-m-d', $timestamp);
    //                         $created_at = date('Y-m-d H:i:s', $timestamp);
    //                     } catch (\Exception $e) {
    //                         $error_reason = 'Invalid date format: ' . $all_data[3];
    //                         $error_counter++;
    //                         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the skipped data
    //                         $count++;
    //                         continue;
    //                     }
    //                     $class = Classes::where('name', 'like', '%' . $all_data[6] . '%')->where('owned_by', $branch->id)->first();
    //                     if (!$class) {
    //                         $error_reason = 'Class not found: ' . $all_data[6] . ' for branch: ' . $branch->name;
    //                         $error_counter++;
    //                         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
    //                         $skipped_records[] = $all_data; // Store the
    //                         $count++;
    //                         continue;
    //                     }
    //                     // Create new student registration
    //                     $stdreg = new StudentRegistration();
    //                     $stdreg->reg_no = $all_data[2];
    //                     $stdreg->regdate = $regdate;
    //                     $stdreg->stdname = $all_data[4] ?? '';
    //                     $stdreg->fathername = $all_data[5] ?? '';
    //                     $stdreg->fatherphone = $all_data[7] ?? '';
    //                     $stdreg->branch = $branch->id;
    //                     $stdreg->session_id = 2;
    //                     $stdreg->active_status = $act_status;
    //                     $stdreg->student_status = $std_status;
    //                     $stdreg->registrationfee = $all_data[8] ?? 0;
    //                     $stdreg->class_id = $class->id;
    //                     $stdreg->reg_class = $class->id;
    //                     $stdreg->register_option = 1;
    //                     $stdreg->owned_by = $branch->id;
    //                     $stdreg->created_by = auth()->user()->id;
    //                     $stdreg->save();
    //                     $stdreg->created_at = $created_at;
    //                     $stdreg->updated_at = $created_at;
    //                     $stdreg->save();

    //                     if ($stdreg) {
    //                         $reg_dis = Registring_option::where('id', 1)
    //                             ->where('created_by', \Auth::user()->creatorId())
    //                             ->first();

    //                         // Create new challan
    //                         $challan = new Challans();
    //                         $challan->student_id = $stdreg->id;
    //                         $challan->class_id = $class->id ?? null;
    //                         $challan->challanNo = $this->challanNo();
    //                         $challan->challan_date = $regdate;
    //                         $challan->challan_type = 'Registration';
    //                         $challan->total_amount = $reg_dis->discount ?? 0;
    //                         $challan->paid_amount = $reg_dis->discount ?? 0;
    //                         $challan->issue_date = $regdate;

    //                         // Handle due date (one week after registration date)
    //                         try {
    //                             $due_date = Carbon::parse($regdate)->addWeek()->toDateString();
    //                         } catch (\Exception $e) {
    //                             $due_date = Carbon::now()->addWeek()->toDateString();
    //                         }

    //                         $challan->due_date = $due_date;
    //                         $challan->status = 'Paid';
    //                         $challan->session_id = $stdreg->session_id;
    //                         $challan->owned_by = $stdreg->owned_by;
    //                         $challan->created_by = \Auth::user()->creatorId();
    //                         $challan->save();
    //                         $challan->created_at = $created_at;
    //                         $challan->updated_at = $created_at;
    //                         $challan->save();

    //                         // Find bank account
    //                         $bankAccount = BankAccount::where('owned_by', $challan->owned_by)
    //                             ->where('bank_name', 'like', '%CSH')->first();
    //                         // dd($bankAccount,$all_data);
    //                         if (!$bankAccount) {
    //                             $error_reason = 'Bank account not found for owner ID: ' . $challan->owned_by;
    //                             $error_counter++;
    //                             $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
    //                             $skipped_records[] = $all_data; // Store the
    //                             $count++;
    //                             continue;
    //                         }

    //                         // Create student receipt
    //                         $recipts = new StudentReceipt();
    //                         $recipts->recipt_date = $regdate;
    //                         $recipts->challan_id = $challan->id;
    //                         $recipts->recipt_amount = $challan->paid_amount;
    //                         $recipts->student_id = $challan->student_id;
    //                         $recipts->challan_amount = $challan->paid_amount;
    //                         $recipts->late_amount = 0;
    //                         $recipts->arrears = 0;
    //                         $recipts->bank_id = $bankAccount->id;
    //                         $recipts->referance = 'Registration Fee';
    //                         $recipts->receive_type = 'CD';
    //                         $recipts->received_by = Auth::user()->id;
    //                         $recipts->owned_by = $challan->owned_by;
    //                         $recipts->created_by = \Auth::user()->creatorId();
    //                         $recipts->save();
    //                         $recipts->created_at = $created_at;
    //                         $recipts->updated_at = $created_at;
    //                         $recipts->save();

    //                         // Find registration fee head
    //                         $pattern = '%registration%';
    //                         $adm_fee_head = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($pattern)])->first();
    //                         if (!$adm_fee_head) {
    //                             $error_reason = 'Registration fee head not found';
    //                             $error_counter++;
    //                             $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
    //                             $skipped_records[] = $all_data; // Store the
    //                             $count++;
    //                             continue;
    //                             // throw new \Exception('Registration fee head not found');
    //                         }

    //                         // Create challan head
    //                         $challan_head = new ChallanHead();
    //                         $challan_head->challan_id = $challan->id;
    //                         $challan_head->head_id = $adm_fee_head->id;
    //                         $challan_head->price = $reg_dis->discount ?? 0;
    //                         $challan_head->concession = 0;
    //                         $challan_head->save();
    //                         $challan_head->created_at = $created_at;
    //                         $challan_head->updated_at = $created_at;
    //                         $challan_head->save();

    //                         $item['0']['head'] = $challan_head->head_id;
    //                         $item['0']['price'] = $reg_dis->discount ?? 0;
    //                         $item['0']['quantity'] = 1;
    //                         $item['0']['concession'] = 0;
    //                         $item['0']['total'] = $reg_dis->discount ?? 0;

    //                         $data = [
    //                             'id' => $challan->id,
    //                             'no' => $challan->challanNo,
    //                             'date' => $challan->challan_date,
    //                             'recipt' => $recipts->id,
    //                             'reference' => $challan->student_id,
    //                             'description' => 'Registration Fee Amount',
    //                             'user_id' => $challan->student_id,
    //                             'amount' => $challan->paid_amount,
    //                             'total' => $challan->paid_amount,
    //                             'user_type' => 'Student',
    //                             'category' => 'Registration',
    //                             'owned_by' => $challan->owned_by,
    //                             'created_by' => $challan->created_by,
    //                             'account_id' => $bankAccount->chart_account_id,
    //                             'items' => $item,
    //                             'created_at' => $created_at
    //                         ];

    //                         // Update registration fee
    //                         $updateStdreg = StudentRegistration::find($stdreg->id);
    //                         if ($updateStdreg) {
    //                             $updateStdreg->registrationfee = $reg_dis->discount ?? 0;
    //                             $updateStdreg->created_at = $created_at;
    //                             $updateStdreg->save();
    //                         }

    //                         // Update bank account balance
    //                         if ($bankAccount->id) {
    //                             Utility::bankAccountBalance($bankAccount->id, $challan->paid_amount, 'credit');
    //                         }

    //                         // Create accounting entries
    //                         $dataret = Utility::crv_entry($data);
    //                         $challan->voucher_id = $dataret;
    //                         $challan->save();
    //                     }
    //                     $success_counter++;
    //                 }
    //                 $count++;
    //             }

    //             fclose($handle);
    //             DB::commit();

    //             // Export error records to Excel if any exist
    //             if (!empty($error_records)) {
    //                 $export_filename = 'registration_import_errors_' . time() . '.csv';
    //                 $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
    //                 $error_file = fopen($error_filepath, 'w+');
    //                 fputcsv($error_file, ['Row', 'Registration No', 'Student Name', 'Error']);
    //                 foreach ($error_records as $row) {
    //                     fputcsv($error_file, $row);
    //                 }
    //                 fclose($error_file);
    //                 return response()->download($error_filepath)->deleteFileAfterSend(true);
    //             }
    //             $message = "{$success_counter} Student Registration added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.";

    //             return redirect()->back()->with('message', $message);
    //         }

    //         throw new \Exception("Unable to open file: {$filepath}");

    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         dd($e);
    //         \Log::error('Registration Import Error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
    //         return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     }
    // }

    private function RegistrationImport($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize error records tracking
        $error_records = [];
        $error_records[] = ['Row', 'Registration No', 'Student Name', 'Reason']; // Header row
        $skipped_records = []; // Array to gather records that were skipped

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;
                $success_counter = 0;
                $error_counter = 0;
                $duplication_counter = 0;
                while (($all_data = fgetcsv($handle, 4000, ",")) !== FALSE) {
                    if ($count > 0) {
                        $error_reason = null;

                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $error_reason = 'Branch name is empty';
                            $error_counter++;
                            $error_records[] = [$count, $all_data[0] ?? 'N/A', $all_data[1] ?? 'N/A', $error_reason];
                            $skipped_records[] = $all_data; // Store the skipped data
                            $count++;
                            continue;
                        }
                        // dd($all_data);
                        //0 branch
                        //1 rollno
                        //2 regno
                        //3 stdname
                        //4 class
                        //5 gender
                        //6 father name
                        //7 father cnic
                        //8 father occupation
                        //9 mother name
                        //10 mother cnic
                        //11 mother occupation
                        //12 reg date
                        //13 adm date 
                        //14 dob
                        //15 contact no
                        //16 address
                        // dd($all_data);
                        $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[0]);
                        $branch = User::where('name', $clean_title)->first();
                        if (!$branch) {
                            $error_reason = 'Branch not found: ' . $clean_title;
                            $error_counter++;
                            $error_records[] = [$count, $all_data[0] ?? 'N/A', $all_data[0] ?? 'N/A', $error_reason];
                            $skipped_records[] = $all_data; // Store the skipped data
                            $count++;
                            continue;
                        }

                        // Check if student ID exists
                        if (empty($all_data[2])) {
                            $error_reason = 'Registration number is empty';
                            $error_counter++;
                            $error_records[] = [$count, 'N/A', $all_data[2] ?? 'N/A', $error_reason];
                            $skipped_records[] = $all_data; // Store the skipped data
                            $count++;
                            continue;
                        }

                        try {
                            $timestamp = strtotime($all_data[12]); // Convert string to timestamp
                            if ($timestamp == false) {
                                throw new \Exception("Invalid date format");
                            }
                            $regdate = date('Y-m-d', $timestamp);
                            $created_at = date('Y-m-d H:i:s', $timestamp);
                        } catch (\Exception $e) {
                            $error_reason = 'Invalid date format: ' . $all_data[12];
                            $error_counter++;
                            $error_records[] = [$count, $all_data[2], $all_data[12] ?? 'N/A', $error_reason];
                            $skipped_records[] = $all_data; // Store the skipped data
                            $count++;
                            continue;
                        }

                        $class = Classes::where('name', 'like', '%' . $all_data[4] . '%')->where('owned_by', $branch->id)->first();
                        if (!$class) {
                            $error_reason = 'Class not found: ' . $all_data[4] . ' for branch: ' . $branch->name;
                            $error_counter++;
                            $error_records[] = [$count, $all_data[2], $all_data[4] ?? 'N/A', $error_reason];
                            $skipped_records[] = $all_data; // Store the
                            $count++;
                            continue;
                        }
                        $reg = StudentRegistration::where('reg_no', $all_data[2])->where('roll_no', $all_data[1])->first();
                        if ($reg) {
                            $reg->regdate = date('Y-m-d', strtotime($all_data[12]));
                            $reg->dob = date('Y-m-d', strtotime($all_data[14]));
                            $reg->stdname = $all_data[3] ?? '';
                            $reg->gender = $all_data[5] ?? '';
                            $reg->fathercnic = $all_data[7] ?? '';
                            $reg->fathername = $all_data[6] ?? '';
                            $reg->fatherprofession = $all_data[8] ?? '';
                            $reg->mothername = $all_data[9] ?? '';
                            $reg->mothercnic = $all_data[10] ?? '';
                            $reg->motherprofession = $all_data[11] ?? '';
                            $reg->address = $all_data[17] ?? '';
                            // $reg->registrationfee = $all_data[8] ?? 0;
                            $reg->reg_class = $class->id;
                            $reg->register_option = 1;
                            if ($reg->student_status != 'Enrolled') {
                                $reg->active_status = 0;
                                $reg->student_status = 'other';

                            }
                            $reg->save();
                            $reg->created_at = $created_at;
                            $reg->updated_at = $created_at;
                            $reg->save();
                            //enrollment update
                            $enr = StudentEnrollments::where('enrollId', $all_data[1])->
                                where('regId', $reg->id)->first();
                            if ($enr) {
                                $enr->regId = $reg->id;
                                $enr->adm_branch = $branch->id;
                                $enr->adm_session = 2;
                                $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                if ($reg->student_status != 'Enrolled') {
                                    $enr->active_status = 0;
                                }
                                $enr->save();
                            } else {
                                // dd('enr not found at first positon');
                                $enr = StudentEnrollments::where('enrollId', $all_data[1])->first();
                                if ($enr) {
                                    $enr->regId = $reg->id;
                                    $enr->adm_branch = $branch->id;
                                    $enr->adm_session = 2;
                                    $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                    if ($reg->student_status != 'Enrolled') {
                                        $enr->active_status = 0;
                                    }
                                    $enr->save();
                                } else {
                                    $enr = StudentEnrollments::where('regId', $reg->id)->first();
                                    if ($enr) {
                                        $enr->enrollId = $all_data[1];
                                        $enr->adm_branch = $branch->id;
                                        $enr->adm_session = 2;
                                        $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                        if ($reg->student_status != 'Enrolled') {
                                            $enr->active_status = 0;
                                        }
                                        $enr->save();
                                    } else {
                                        $enr = new StudentEnrollments();
                                        $enr->enrollId = $all_data[1];
                                        $enr->regId = $reg->id;
                                        $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                        $enr->class_id = $class->id;
                                        $enr->adm_session = 2;
                                        $enr->adm_branch = $branch->id;
                                        $enr->session_id = 2;
                                        $enr->owned_by = $branch->id;
                                        $enr->created_at = $created_at;
                                        $enr->save();
                                        $reg->roll_no = $all_data[1];
                                        $reg->save();
                                        $reg->created_at = $created_at;
                                        $reg->updated_at = $created_at;
                                        $reg->save();
                                    }
                                }
                            }
                        } else {
                            $reg = StudentRegistration::where('roll_no', $all_data[1])
                                ->first();
                            if ($reg) {
                                if ($reg->student_status == 'Enrolled' || $reg->student_status == 'other') {
                                    dd(
                                        $all_data,
                                        'already registered student with this roll no'
                                    );
                                } else {
                                    $reg->reg_no = $all_data[2];
                                    $reg->regdate = date('Y-m-d', strtotime($all_data[12]));
                                    $reg->dob = date('Y-m-d', strtotime($all_data[14]));
                                    $reg->stdname = $all_data[3] ?? '';
                                    $reg->gender = $all_data[5] ?? '';
                                    $reg->fathercnic = $all_data[7] ?? '';
                                    $reg->fathername = $all_data[6] ?? '';
                                    $reg->fatherprofession = $all_data[8] ?? '';
                                    $reg->mothername = $all_data[9] ?? '';
                                    $reg->mothercnic = $all_data[10] ?? '';
                                    $reg->motherprofession = $all_data[11] ?? '';
                                    $reg->address = $all_data[17] ?? '';
                                    if ($reg->student_status != 'Enrolled') {
                                        $reg->active_status = 0;
                                        $reg->student_status = 'other';
                                    }
                                    ;
                                    $reg->reg_class = $class->id;
                                    $reg->register_option = 1;
                                    $reg->save();
                                    $reg->created_at = $created_at;
                                    $reg->updated_at = $created_at;
                                    $reg->save();
                                    //enrollment update
                                    $enr = StudentEnrollments::where('enrollId', $all_data[1])
                                        ->where('regId', $reg->id)->first();
                                    if ($enr) {
                                        $enr->regId = $reg->id;
                                        $enr->adm_branch = $branch->id;
                                        $enr->adm_session = 2;
                                        $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                        $enr->save();
                                    } else {
                                        // dd('enr not found at second positon');
                                        $enr = StudentEnrollments::where('enrollId', $all_data[1])->first();
                                        if ($enr) {
                                            $enr->regId = $reg->id;
                                            $enr->adm_branch = $branch->id;
                                            $enr->adm_session = 2;
                                            $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                            if ($reg->student_status != 'Enrolled') {
                                                $enr->active_status = 0;
                                            }
                                            $enr->save();
                                        } else {
                                            $enr = StudentEnrollments::where('regId', $reg->id)->first();
                                            if ($enr) {
                                                $enr->enrollId = $all_data[1];
                                                $enr->adm_branch = $branch->id;
                                                $enr->adm_session = 2;
                                                $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                if ($reg->student_status != 'Enrolled') {
                                                    $enr->active_status = 0;
                                                }
                                                $enr->save();
                                            } else {
                                                $enr = new StudentEnrollments();
                                                $enr->enrollId = $all_data[1];
                                                $enr->regId = $reg->id;
                                                $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                $enr->class_id = $class->id;
                                                $enr->adm_session = 2;
                                                $enr->adm_branch = $branch->id;
                                                $enr->session_id = 2;
                                                $enr->owned_by = $branch->id;
                                                $enr->created_at = $created_at;
                                                $enr->save();
                                                $reg->roll_no = $all_data[1];
                                                $reg->save();
                                                $reg->created_at = $created_at;
                                                $reg->updated_at = $created_at;
                                                $reg->save();
                                            }
                                        }

                                    }
                                }
                            } else {
                                $reg = StudentRegistration::where('reg_no', $all_data[2])
                                    ->whereNotIn('student_status', ['Enrolled'])->first();
                                if ($reg) {
                                    $reg->roll_no = $all_data[1];
                                    $reg->reg_no = $all_data[2];
                                    $reg->regdate = date('Y-m-d', strtotime($all_data[12]));
                                    $reg->dob = date('Y-m-d', strtotime($all_data[14]));
                                    $reg->stdname = $all_data[3] ?? '';
                                    $reg->gender = $all_data[5] ?? '';
                                    $reg->fathercnic = $all_data[7] ?? '';
                                    $reg->fathername = $all_data[6] ?? '';
                                    $reg->fatherprofession = $all_data[8] ?? '';
                                    $reg->mothername = $all_data[9] ?? '';
                                    $reg->mothercnic = $all_data[10] ?? '';
                                    $reg->motherprofession = $all_data[11] ?? '';
                                    $reg->address = $all_data[17] ?? '';
                                    if ($reg->student_status != 'Enrolled') {
                                        $reg->active_status = 0;
                                        $reg->student_status = 'other';
                                    }
                                    // $reg->registrationfee = $all_data[8] ?? 0;
                                    $reg->reg_class = $class->id;
                                    $reg->register_option = 1;
                                    $reg->save();
                                    $reg->created_at = $created_at;
                                    $reg->updated_at = $created_at;
                                    $reg->save();
                                    //enrollment update
                                    $enr = StudentEnrollments::where('enrollId', $all_data[1])
                                        ->where('regId', $reg->id)->first();
                                    if ($enr) {
                                        $enr->regId = $reg->id;
                                        $enr->adm_branch = $branch->id;
                                        $enr->adm_session = 2;
                                        $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                        $enr->save();
                                    } else {
                                        // dd('enr not found at third positon');
                                        $enr = StudentEnrollments::where('enrollId', $all_data[1])->first();
                                        if ($enr) {
                                            $enr->regId = $reg->id;
                                            $enr->adm_branch = $branch->id;
                                            $enr->adm_session = 2;
                                            $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                            if ($reg->student_status != 'Enrolled') {
                                                $enr->active_status = 0;
                                            }
                                            $enr->save();
                                        } else {
                                            $enr = StudentEnrollments::where('regId', $reg->id)->first();
                                            if ($enr) {
                                                $enr->enrollId = $all_data[1];
                                                $enr->adm_branch = $branch->id;
                                                $enr->adm_session = 2;
                                                $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                if ($reg->student_status != 'Enrolled') {
                                                    $enr->active_status = 0;
                                                }
                                                $enr->save();
                                            } else {
                                                $enr = new StudentEnrollments();
                                                $enr->enrollId = $all_data[1];
                                                $enr->regId = $reg->id;
                                                $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                $enr->class_id = $class->id;
                                                $enr->adm_session = 2;
                                                $enr->adm_branch = $branch->id;
                                                $enr->session_id = 2;
                                                $enr->owned_by = $branch->id;
                                                $enr->created_at = $created_at;
                                                $enr->save();
                                                $reg->roll_no = $all_data[1];
                                                $reg->save();
                                                $reg->created_at = $created_at;
                                                $reg->updated_at = $created_at;
                                                $reg->save();
                                            }
                                        }

                                    }
                                } else {
                                    //on basis of name
                                    $reg = StudentRegistration::where('stdname', 'like', '%' . $all_data[3] . '%')
                                        ->whereNotIn('student_status', ['other', 'withdrawl', 'Enrolled', 'Registered'])
                                        ->first();
                                    if ($reg) {
                                        $reg->roll_no = $all_data[1];
                                        $reg->reg_no = $all_data[2];
                                        $reg->regdate = date('Y-m-d', strtotime($all_data[12]));
                                        $reg->dob = date('Y-m-d', strtotime($all_data[14]));
                                        $reg->gender = $all_data[5] ?? '';
                                        $reg->fathercnic = $all_data[7] ?? '';
                                        $reg->fathername = $all_data[6] ?? '';
                                        $reg->fatherprofession = $all_data[8] ?? '';
                                        $reg->mothername = $all_data[9] ?? '';
                                        $reg->mothercnic = $all_data[10] ?? '';
                                        $reg->motherprofession = $all_data[11] ?? '';
                                        $reg->address = $all_data[17] ?? '';
                                        if ($reg->student_status != 'Enrolled') {
                                            $reg->active_status = 0;
                                            $reg->student_status = 'other';
                                        }
                                        $reg->reg_class = $class->id;
                                        $reg->register_option = 1;
                                        $reg->save();
                                        $reg->created_at = $created_at;
                                        $reg->updated_at = $created_at;
                                        $reg->save();
                                        //enrollment update
                                        $enr = StudentEnrollments::where('enrollId', $all_data[1])->where('regId', $reg->id)->first();
                                        if ($enr) {
                                            $enr->regId = $reg->id;
                                            $enr->adm_branch = $branch->id;
                                            $enr->adm_session = 2;
                                            $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                            $enr->save();
                                        } else {
                                            // dd('enr not found at fourth positon');
                                            $enr = StudentEnrollments::where('enrollId', $all_data[1])->first();
                                            if ($enr) {
                                                $enr->regId = $reg->id;
                                                $enr->adm_branch = $branch->id;
                                                $enr->adm_session = 2;
                                                $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                if ($reg->student_status != 'Enrolled') {
                                                    $enr->active_status = 0;
                                                }
                                                $enr->save();
                                            } else {
                                                $enr = StudentEnrollments::where('regId', $reg->id)->first();
                                                if ($enr) {
                                                    $enr->enrollId = $all_data[1];
                                                    $enr->adm_branch = $branch->id;
                                                    $enr->adm_session = 2;
                                                    $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                    if ($reg->student_status != 'Enrolled') {
                                                        $enr->active_status = 0;
                                                    }
                                                    $enr->save();
                                                } else {
                                                    $enr = new StudentEnrollments();
                                                    $enr->enrollId = $all_data[1];
                                                    $enr->regId = $reg->id;
                                                    $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                                    $enr->class_id = $class->id;
                                                    $enr->adm_session = 2;
                                                    $enr->adm_branch = $branch->id;
                                                    $enr->session_id = 2;
                                                    $enr->owned_by = $branch->id;
                                                    $enr->created_at = $created_at;
                                                    $enr->save();
                                                    $reg->roll_no = $all_data[1];
                                                    $reg->save();
                                                    $reg->created_at = $created_at;
                                                    $reg->updated_at = $created_at;
                                                    $reg->save();
                                                }
                                            }
                                        }
                                    } else {
                                        //new 
                                        $reg = new StudentRegistration();
                                        $reg->roll_no = $all_data[1];
                                        $reg->reg_no = $all_data[2];
                                        $reg->regdate = date('Y-m-d', strtotime($all_data[12]));
                                        $reg->dob = date('Y-m-d', strtotime($all_data[14]));
                                        $reg->stdname = $all_data[3] ?? '';
                                        $reg->gender = $all_data[5] ?? '';
                                        $reg->fathercnic = $all_data[7] ?? '';
                                        $reg->fathername = $all_data[6] ?? '';
                                        $reg->fatherprofession = $all_data[8] ?? '';
                                        $reg->mothername = $all_data[9] ?? '';
                                        $reg->mothercnic = $all_data[10] ?? '';
                                        $reg->motherprofession = $all_data[11] ?? '';
                                        $reg->address = $all_data[17] ?? '';
                                        $reg->active_status = 0;
                                        $reg->student_status = 'other';
                                        $reg->reg_class = $class->id;
                                        $reg->register_option = 1;
                                        $reg->save();
                                        $reg->created_at = $created_at;
                                        $reg->updated_at = $created_at;
                                        $reg->save();
                                        //enrollment add
                                        $enr = new StudentEnrollments();
                                        $enr->enrollId = $all_data[1];
                                        $enr->regId = $reg->id;
                                        $enr->adm_date = date('Y-m-d', strtotime($all_data[13]));
                                        $enr->class_id = $class->id;
                                        $enr->adm_session = 2;
                                        $enr->adm_branch = $branch->id;
                                        $enr->session_id = 2;
                                        $enr->active_status = 0;
                                        $enr->owned_by = $branch->id;
                                        $enr->created_at = $created_at;
                                        $enr->save();

                                    }

                                }
                            }
                            // if (!empty($all_data[16] && $all_data[16] != 0)) {
                            //     // Create new challan
                            //     $challan = new Challans();
                            //     $challan->student_id = $stdreg->id;
                            //     $challan->class_id = $class->id ?? null;
                            //     $challan->challanNo = $this->challanNo();
                            //     $challan->challan_date = $regdate;
                            //     $challan->challan_type = 'Registration';
                            //     $challan->total_amount = $all_data[16] ?? 0;
                            //     $challan->paid_amount = $all_data[16] ?? 0;
                            //     $challan->issue_date = $regdate;

                            //     // Handle due date (one week after registration date)
                            //     try {
                            //         $due_date = Carbon::parse($regdate)->addWeek()->toDateString();
                            //     } catch (\Exception $e) {
                            //         $due_date = Carbon::now()->addWeek()->toDateString();
                            //     }

                            //     $challan->due_date = $due_date;
                            //     $challan->status = 'Paid';
                            //     $challan->session_id = $stdreg->session_id;
                            //     $challan->owned_by = $stdreg->owned_by;
                            //     $challan->created_by = \Auth::user()->creatorId();
                            //     $challan->save();
                            //     $challan->created_at = $created_at;
                            //     $challan->updated_at = $created_at;
                            //     $challan->save();

                            //     // Find bank account
                            //     $bankAccount = BankAccount::where('owned_by', $challan->owned_by)
                            //         ->where('bank_name', 'like', '%CSH')->first();
                            //     // dd($bankAccount,$all_data);
                            //     if (!$bankAccount) {
                            //         $error_reason = 'Bank account not found for owner ID: ' . $challan->owned_by;
                            //         $error_counter++;
                            //         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                            //         $skipped_records[] = $all_data; // Store the
                            //         $count++;
                            //         continue;
                            //     }

                            //     // Create student receipt
                            //     $recipts = new StudentReceipt();
                            //     $recipts->recipt_date = $regdate;
                            //     $recipts->challan_id = $challan->id;
                            //     $recipts->recipt_amount = $challan->paid_amount;
                            //     $recipts->student_id = $challan->student_id;
                            //     $recipts->challan_amount = $challan->paid_amount;
                            //     $recipts->late_amount = 0;
                            //     $recipts->arrears = 0;
                            //     $recipts->bank_id = $bankAccount->id;
                            //     $recipts->referance = 'Registration Fee';
                            //     $recipts->receive_type = 'CD';
                            //     $recipts->received_by = Auth::user()->id;
                            //     $recipts->owned_by = $challan->owned_by;
                            //     $recipts->created_by = \Auth::user()->creatorId();
                            //     $recipts->save();
                            //     $recipts->created_at = $created_at;
                            //     $recipts->updated_at = $created_at;
                            //     $recipts->save();

                            //     // Find registration fee head
                            //     $pattern = '%registration%';
                            //     $adm_fee_head = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($pattern)])->first();
                            //     if (!$adm_fee_head) {
                            //         $error_reason = 'Registration fee head not found';
                            //         $error_counter++;
                            //         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                            //         $skipped_records[] = $all_data; // Store the
                            //         $count++;
                            //         continue;
                            //         // throw new \Exception('Registration fee head not found');
                            //     }

                            //     // Create challan head
                            //     $challan_head = new ChallanHead();
                            //     $challan_head->challan_id = $challan->id;
                            //     $challan_head->head_id = $adm_fee_head->id;
                            //     $challan_head->price = $all_data[16] ?? 0;
                            //     $challan_head->concession = 0;
                            //     $challan_head->save();
                            //     $challan_head->created_at = $created_at;
                            //     $challan_head->updated_at = $created_at;
                            //     $challan_head->save();

                            //     $item['0']['head'] = $challan_head->head_id;
                            //     $item['0']['price'] = $all_data[16] ?? 0;
                            //     $item['0']['quantity'] = 1;
                            //     $item['0']['concession'] = 0;
                            //     $item['0']['total'] = $all_data[16] ?? 0;

                            //     $data = [
                            //         'id' => $challan->id,
                            //         'no' => $challan->challanNo,
                            //         'date' => $challan->challan_date,
                            //         'recipt' => $recipts->id,
                            //         'reference' => $challan->student_id,
                            //         'description' => 'Registration Fee Amount',
                            //         'user_id' => $challan->student_id,
                            //         'amount' => $challan->paid_amount,
                            //         'total' => $challan->paid_amount,
                            //         'user_type' => 'Student',
                            //         'category' => 'Registration',
                            //         'owned_by' => $challan->owned_by,
                            //         'created_by' => $challan->created_by,
                            //         'account_id' => $bankAccount->chart_account_id,
                            //         'items' => $item,
                            //         'created_at' => $created_at
                            //     ];

                            //     // Update registration fee
                            //     $updateStdreg = StudentRegistration::find($stdreg->id);
                            //     if ($updateStdreg) {
                            //         $updateStdreg->registrationfee = $all_data[16] ?? 0;
                            //         $updateStdreg->created_at = $created_at;
                            //         $updateStdreg->save();
                            //     }

                            //     // Update bank account balance
                            //     if ($bankAccount->id) {
                            //         Utility::bankAccountBalance($bankAccount->id, $challan->paid_amount, 'credit');
                            //     }

                            //     // Create accounting entries
                            //     $dataret = Utility::crv_entry($data);
                            //     $challan->voucher_id = $dataret;
                            //     $challan->save();
                            // }
                        }
                        $success_counter++;
                    }
                    // if ($count > 0) {
                    //     $error_reason = null;

                    //     // Check for empty data in first column
                    //     if (empty($all_data[0])) {
                    //         $error_reason = 'Branch name is empty';
                    //         $error_counter++;
                    //         $error_records[] = [$count, $all_data[1] ?? 'N/A', $all_data[2] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the skipped data
                    //         $count++;
                    //         continue;
                    //     }
                    //     //0 branch
                    //     //2 regno
                    //     //4 name
                    //     //5 father name
                    //     //6 class
                    //     $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[0]);
                    //     $branch = User::where('name', $clean_title)->first();
                    //     if (!$branch) {
                    //         $error_reason = 'Branch not found: ' . $clean_title;
                    //         $error_counter++;
                    //         $error_records[] = [$count, $all_data[0] ?? 'N/A', $all_data[1] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the skipped data
                    //         $count++;
                    //         continue;
                    //     }

                    //     // Check if student ID exists
                    //     if (empty($all_data[2])) {
                    //         $error_reason = 'Registration number is empty';
                    //         $error_counter++;
                    //         $error_records[] = [$count, 'N/A', $all_data[2] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the skipped data
                    //         $count++;
                    //         continue;
                    //     }

                    //     // Check for duplicate student registration
                    //     $duplicate_user_check = StudentRegistration::where('reg_no', $all_data[2])->get();
                    //     if ($duplicate_user_check->count() > 0) {
                    //         $error_reason = 'Duplicate registration number';
                    //         $error_counter++;
                    //         $duplication_counter++;
                    //         $error_records[] = [$count, $all_data[2], $all_data[1] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the skipped data
                    //         $count++;
                    //         continue;
                    //     }

                    //     // Set status values
                    //     $act_status = isset($all_data[9]) && strtoupper($all_data[9]) == 'YES' ? 1 : 0;
                    //     $std_status = isset($all_data[9]) && strtoupper($all_data[9]) == 'YES' ? 'Enrolled' : 'Registered';

                    //     // Format date - assuming format is d/m/Y (1/4/2019 = 4 Jan 2019)
                    //     $regdate = $all_data[3] ?? null;
                    //     if (!$regdate) {
                    //         $error_reason = 'Registration date is missing';
                    //         $error_counter++;
                    //         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the skipped data
                    //         $count++;
                    //         continue;
                    //     }

                    //     try {
                    //         $timestamp = strtotime($regdate); // Convert string to timestamp
                    //         if ($timestamp === false) {
                    //             throw new \Exception("Invalid date format");
                    //         }
                    //         $regdate = date('Y-m-d', $timestamp);
                    //         $created_at = date('Y-m-d H:i:s', $timestamp);
                    //     } catch (\Exception $e) {
                    //         $error_reason = 'Invalid date format: ' . $all_data[3];
                    //         $error_counter++;
                    //         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the skipped data
                    //         $count++;
                    //         continue;
                    //     }
                    //     $class = Classes::where('name', 'like', '%' . $all_data[6] . '%')->where('owned_by', $branch->id)->first();
                    //     if (!$class) {
                    //         $error_reason = 'Class not found: ' . $all_data[6] . ' for branch: ' . $branch->name;
                    //         $error_counter++;
                    //         $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                    //         $skipped_records[] = $all_data; // Store the
                    //         $count++;
                    //         continue;
                    //     }
                    //     // Create new student registration
                    //     $stdreg = new StudentRegistration();
                    //     $stdreg->reg_no = $all_data[2];
                    //     $stdreg->regdate = $regdate;
                    //     $stdreg->stdname = $all_data[4] ?? '';
                    //     $stdreg->fathername = $all_data[5] ?? '';
                    //     $stdreg->fatherphone = $all_data[7] ?? '';
                    //     $stdreg->branch = $branch->id;
                    //     $stdreg->session_id = 2;
                    //     $stdreg->active_status = $act_status;
                    //     $stdreg->student_status = $std_status;
                    //     $stdreg->registrationfee = $all_data[8] ?? 0;
                    //     $stdreg->class_id = $class->id;
                    //     $stdreg->reg_class = $class->id;
                    //     $stdreg->register_option = 1;
                    //     $stdreg->owned_by = $branch->id;
                    //     $stdreg->created_by = auth()->user()->id;
                    //     $stdreg->save();
                    //     $stdreg->created_at = $created_at;
                    //     $stdreg->updated_at = $created_at;
                    //     $stdreg->save();

                    //     if ($stdreg) {
                    //         $reg_dis = Registring_option::where('id', 1)
                    //             ->where('created_by', \Auth::user()->creatorId())
                    //             ->first();

                    //         // Create new challan
                    //         $challan = new Challans();
                    //         $challan->student_id = $stdreg->id;
                    //         $challan->class_id = $class->id ?? null;
                    //         $challan->challanNo = $this->challanNo();
                    //         $challan->challan_date = $regdate;
                    //         $challan->challan_type = 'Registration';
                    //         $challan->total_amount = $reg_dis->discount ?? 0;
                    //         $challan->paid_amount = $reg_dis->discount ?? 0;
                    //         $challan->issue_date = $regdate;

                    //         // Handle due date (one week after registration date)
                    //         try {
                    //             $due_date = Carbon::parse($regdate)->addWeek()->toDateString();
                    //         } catch (\Exception $e) {
                    //             $due_date = Carbon::now()->addWeek()->toDateString();
                    //         }

                    //         $challan->due_date = $due_date;
                    //         $challan->status = 'Paid';
                    //         $challan->session_id = $stdreg->session_id;
                    //         $challan->owned_by = $stdreg->owned_by;
                    //         $challan->created_by = \Auth::user()->creatorId();
                    //         $challan->save();
                    //         $challan->created_at = $created_at;
                    //         $challan->updated_at = $created_at;
                    //         $challan->save();

                    //         // Find bank account
                    //         $bankAccount = BankAccount::where('owned_by', $challan->owned_by)
                    //             ->where('bank_name', 'like', '%CSH')->first();
                    //         // dd($bankAccount,$all_data);
                    //         if (!$bankAccount) {
                    //             $error_reason = 'Bank account not found for owner ID: ' . $challan->owned_by;
                    //             $error_counter++;
                    //             $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                    //             $skipped_records[] = $all_data; // Store the
                    //             $count++;
                    //             continue;
                    //         }

                    //         // Create student receipt
                    //         $recipts = new StudentReceipt();
                    //         $recipts->recipt_date = $regdate;
                    //         $recipts->challan_id = $challan->id;
                    //         $recipts->recipt_amount = $challan->paid_amount;
                    //         $recipts->student_id = $challan->student_id;
                    //         $recipts->challan_amount = $challan->paid_amount;
                    //         $recipts->late_amount = 0;
                    //         $recipts->arrears = 0;
                    //         $recipts->bank_id = $bankAccount->id;
                    //         $recipts->referance = 'Registration Fee';
                    //         $recipts->receive_type = 'CD';
                    //         $recipts->received_by = Auth::user()->id;
                    //         $recipts->owned_by = $challan->owned_by;
                    //         $recipts->created_by = \Auth::user()->creatorId();
                    //         $recipts->save();
                    //         $recipts->created_at = $created_at;
                    //         $recipts->updated_at = $created_at;
                    //         $recipts->save();

                    //         // Find registration fee head
                    //         $pattern = '%registration%';
                    //         $adm_fee_head = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($pattern)])->first();
                    //         if (!$adm_fee_head) {
                    //             $error_reason = 'Registration fee head not found';
                    //             $error_counter++;
                    //             $error_records[] = [$count, $all_data[2], $all_data[3] ?? 'N/A', $error_reason];
                    //             $skipped_records[] = $all_data; // Store the
                    //             $count++;
                    //             continue;
                    //             // throw new \Exception('Registration fee head not found');
                    //         }

                    //         // Create challan head
                    //         $challan_head = new ChallanHead();
                    //         $challan_head->challan_id = $challan->id;
                    //         $challan_head->head_id = $adm_fee_head->id;
                    //         $challan_head->price = $reg_dis->discount ?? 0;
                    //         $challan_head->concession = 0;
                    //         $challan_head->save();
                    //         $challan_head->created_at = $created_at;
                    //         $challan_head->updated_at = $created_at;
                    //         $challan_head->save();

                    //         $item['0']['head'] = $challan_head->head_id;
                    //         $item['0']['price'] = $reg_dis->discount ?? 0;
                    //         $item['0']['quantity'] = 1;
                    //         $item['0']['concession'] = 0;
                    //         $item['0']['total'] = $reg_dis->discount ?? 0;

                    //         $data = [
                    //             'id' => $challan->id,
                    //             'no' => $challan->challanNo,
                    //             'date' => $challan->challan_date,
                    //             'recipt' => $recipts->id,
                    //             'reference' => $challan->student_id,
                    //             'description' => 'Registration Fee Amount',
                    //             'user_id' => $challan->student_id,
                    //             'amount' => $challan->paid_amount,
                    //             'total' => $challan->paid_amount,
                    //             'user_type' => 'Student',
                    //             'category' => 'Registration',
                    //             'owned_by' => $challan->owned_by,
                    //             'created_by' => $challan->created_by,
                    //             'account_id' => $bankAccount->chart_account_id,
                    //             'items' => $item,
                    //             'created_at' => $created_at
                    //         ];

                    //         // Update registration fee
                    //         $updateStdreg = StudentRegistration::find($stdreg->id);
                    //         if ($updateStdreg) {
                    //             $updateStdreg->registrationfee = $reg_dis->discount ?? 0;
                    //             $updateStdreg->created_at = $created_at;
                    //             $updateStdreg->save();
                    //         }

                    //         // Update bank account balance
                    //         if ($bankAccount->id) {
                    //             Utility::bankAccountBalance($bankAccount->id, $challan->paid_amount, 'credit');
                    //         }

                    //         // Create accounting entries
                    //         $dataret = Utility::crv_entry($data);
                    //         $challan->voucher_id = $dataret;
                    //         $challan->save();
                    //     }
                    //     $success_counter++;
                    // }
                    $count++;
                }

                fclose($handle);
                DB::commit();

                // Export error records to Excel if any exist
                if (!empty($error_records)) {
                    $export_filename = 'registration_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Row', 'Registration No', 'Student Name', 'Error']);
                    foreach ($error_records as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                $message = "{$success_counter} Student Registration added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.";

                return redirect()->back()->with('message', $message);
            }

            throw new \Exception("Unable to open file: {$filepath}");

        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            \Log::error('Registration Import Error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function TransferImport($file, $request)
    {
        // dd($file);
        set_time_limit(0);
        $filename = $file->getClientOriginalName();
        $file->move(public_path(path: 'assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;
                while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';
                        // dd($all_data);
                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $reason = 'Empty enrollment ID';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Validate enrollment
                        $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
                        if (!$enr) {
                            $reason = 'Enrollment not found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Get student name
                        $student_name = "N/A";
                        $reg = StudentRegistration::where('reg_no', $enr->regId)->where('owned_by', $enr->owned_by)->first();
                        if ($reg) {
                            $student_name = $reg->stdname;
                        }

                        // Clean and validate branch from data (column 5)
                        $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[5]);
                        $branchfrom = User::where('name', 'like', '%' . $clean_title . '%')->first();
                        if (!$branchfrom) {
                            dd($all_data, 'branchfrom', $clean_title);
                            $reason = 'From branch not found: ' . $all_data[5];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Clean and validate branch to data (column 3)
                        $clean_title_to = preg_replace('/\s*\(.*?\)/', '', $all_data[3]);
                        $branchto = User::where('name', 'like', '%' . $clean_title_to . '%')->first();
                        if (!$branchto) {
                            dd($all_data, 'branchto', $clean_title_to);
                            $reason = 'To branch not found: ' . $all_data[3];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Validate class to and from
                        $classto = Classes::where('name', $all_data[4])->where('owned_by', $branchto->id)->first();
                        if (!$classto) {
                            $classto = new Classes();
                            $classto->name = $all_data[4];
                            $classto->owned_by = $branchto->id;
                            $classto->created_by = auth()->user()->id;
                            $classto->save();
                        }

                        $classfrom = Classes::where('name', $all_data[6])->where('owned_by', $branchfrom->id)->first();
                        if (!$classfrom) {
                            $classfrom = new Classes();
                            $classfrom->name = $all_data[6];
                            $classfrom->owned_by = $branchfrom->id;
                            $classfrom->created_by = auth()->user()->id;
                            $classfrom->save();
                            // $reason = 'Source class not found: ' . $all_data[6];
                            // $error_counter++;
                            // $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            // $count++;
                            // continue;
                        }

                        // Get first section from source class/branch
                        $sectionClassFrom = ClassSection::where('class_id', $classfrom->id)
                            ->where('owned_by', $branchfrom->id)
                            // ->where('active_status', 1)
                            ->first();

                        if (!$sectionClassFrom) {
                            $sectionfrom = new Section();
                            $sectionfrom->name = 'A';
                            $sectionfrom->owned_by = $branchfrom->id;
                            $sectionfrom->created_by = auth()->user()->id;
                            $sectionfrom->save();
                            $sectionClassFrom = new ClassSection();
                            $sectionClassFrom->class_id = $classfrom->id;
                            $sectionClassFrom->section_id = $sectionfrom->id;
                            $sectionClassFrom->owned_by = $branchfrom->id;
                            $sectionClassFrom->created_by = auth()->user()->id;
                            $sectionClassFrom->save();
                        }
                        $sectionfrom = Section::find($sectionClassFrom->section_id);

                        // Get first section from destination class/branch
                        $sectionClassTo = ClassSection::where('class_id', $classto->id)
                            ->where('owned_by', $branchto->id)
                            // ->where('active_status', 1)
                            ->first();

                        if (!$sectionClassTo) {
                            $sectionto = new Section();
                            $sectionto->name = 'A';
                            $sectionto->owned_by = $branchto->id;
                            $sectionto->created_by = auth()->user()->id;
                            $sectionto->save();
                            $sectionClassTo = new ClassSection();
                            $sectionClassTo->class_id = $classto->id;
                            $sectionClassTo->section_id = $sectionto->id;
                            $sectionClassTo->owned_by = $branchto->id;
                            $sectionClassTo->created_by = auth()->user()->id;
                            $sectionClassTo->save();
                        }
                        $sectionto = Section::find($sectionClassTo->section_id);

                        // Check for existing transfer (prevent duplicates)
                        $transf = StudentTransfer::where('student_id', $enr->enrollId)->first();
                        if ($transf) {
                            $reason = 'Student already transferred';
                            $duplication_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Parse transfer date
                        $transfer_date = date('Y-m-d');
                        if (!empty($all_data[9])) {
                            try {
                                $transfer_date = date('Y-m-d', strtotime($all_data[9]));
                            } catch (\Exception $e) {
                                // If date parsing fails, use current date
                                $transfer_date = date('Y-m-d');
                            }
                        }

                        // Determine transfer type
                        $transfer_type = '';
                        if (isset($all_data[12]) && !empty($all_data[12])) {
                            $transfer_type = trim($all_data[12]);
                        }

                        // Create transfer entry
                        $transfer = new StudentTransfer();
                        $transfer->student_id = $enr->enrollId;
                        $transfer->transfer_date = $transfer_date;
                        $transfer->transfer_type = $transfer_type;
                        $transfer->branch_from = $branchfrom->id;
                        $transfer->class_from = $classfrom->id;
                        $transfer->section_from = $sectionfrom->id;
                        $transfer->branch_to = $branchto->id;
                        $transfer->class_to = $classto->id;
                        $transfer->section_to = $sectionto->id;
                        $transfer->reason = isset($all_data[8]) ? $all_data[8] : '';
                        $transfer->session_id = $enr->session_id;
                        $transfer->owned_by = $enr->owned_by;
                        $transfer->created_by = \Auth::user()->creatorId();
                        $transfer->save();
                        //challan
                        $challan = new Challans();
                        $challan->student_id = $all_data[1];
                        $challan->rollno = $all_data[0];
                        $challan->class_id = $classfrom->id;
                        $challan->challanNo = $this->challanNo();
                        $challan->challan_date = $transfer_date;
                        $challan->challan_type = $transfer_type;
                        $challan->issue_date = $transfer_date;
                        $challan->due_date = $transfer_date;
                        $challan->status = 'paid';
                        $challan->session_id = $enr->session_id;
                        $challan->owned_by = $enr->owned_by;
                        $challan->created_by = \Auth::user()->creatorId();
                        $challan->save();
                        $challan->created_at = date('Y-m-d H:i:s', strtotime($transfer_date));
                        $challan->updated_at = date('Y-m-d H:i:s', strtotime($transfer_date));
                        $challan->save();
                        $concessionAmount = 0;
                        $itemIndex = 0;
                        $pattern = '%transfer fee%';
                        $trns_fee_head = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($pattern)])->first();
                        $challan_head = new ChallanHead();
                        $challan_head->challan_id = $challan->id;
                        $challan_head->head_id = $trns_fee_head->id;
                        $challan_head->price = $all_data[7];
                        $challan_head->concession = 0;
                        $challan_head->save();
                        //jv
                        $raw_price = $all_data[7];
                        $clean_price = (float) str_replace(',', '', $raw_price);
                        $clean_price = (int) str_replace(',', '', $raw_price);
                        $item[$itemIndex]['head'] = $trns_fee_head->id;
                        $item[$itemIndex]['price'] = $clean_price;
                        $item[$itemIndex]['quantity'] = 1;
                        $item[$itemIndex]['concession'] = 0;
                        $item[$itemIndex]['total'] = $clean_price;
                        $data['id'] = $challan->id;
                        $data['no'] = $challan->challanNo;
                        $data['date'] = $challan->challan_date;
                        $data['reference'] = $challan->student_id;
                        $data['category'] = 'Transfer Challan';
                        $data['user_id'] = $enr->enrollId;
                        $data['user_type'] = 'Student';
                        $data['owned_by'] = $challan->owned_by;
                        $data['created_by'] = $challan->created_by;
                        $data['created_at'] = date('Y-m-d H:i:s', strtotime($transfer_date));
                        $data['updated_at'] = date('Y-m-d H:i:s', strtotime($transfer_date));
                        $data['items'] = $item;
                        // dd($data);
                        $dataret = Utility::jrentry($data);
                        $challan->voucher_id = $dataret;
                        $challan->save();


                        //
                        $journalItem = new JournalItem();
                        $journalItem->journal = @$challan->voucher_id;
                        $journalItem->account = @$trns_fee_head->account_id;
                        $journalItem->head = @$trns_fee_head->id;
                        $journalItem->description = $trns_fee_head->fee_head;
                        $journalItem->entry_id = @$challan_head->id;
                        $journalItem->types = 'Challan';
                        $journalItem->credit = $clean_price;
                        $journalItem->debit = 0;
                        $journalItem->save();
                        $journalItem->created_at = @$challan->created_at;
                        $journalItem->updated_at = @$challan->updated_at;
                        $journalItem->save();

                        //  reciveable entry
                        $journalItem = new JournalItem();
                        $journalItem->journal = @$challan->voucher_id;
                        $journalItem->account = @$trns_fee_head->receivable_account_id;
                        $journalItem->head = $trns_fee_head->id;
                        $journalItem->description = 'Reciveable of Challan no : ' . @$challan->challanNo;
                        $journalItem->entry_id = @$challan_head->id;
                        $journalItem->types = 'Challan';
                        $journalItem->credit = 0;
                        $journalItem->debit = $clean_price;
                        $journalItem->save();
                        $journalItem->created_at = @$challan->created_at;
                        $journalItem->updated_at = @$challan->updated_at;
                        $journalItem->save();
                        $school = User::find($challan->owned_by);
                        $bankAccount = BankAccount::where('owned_by', $school->id)->first();
                        // dd($all_data,$bankAccount,$challan->owned_by);
                        $recipts = new StudentReceipt();
                        $recipts->recipt_date = date('Y-m-d', strtotime($all_data[2]));
                        $recipts->challan_id = $challan->id;
                        $recipts->recipt_amount = $all_data[7];
                        $recipts->student_id = $challan->student_id;
                        $recipts->challan_amount = $all_data[7];
                        $recipts->late_amount = 0;
                        $recipts->arrears = 0;
                        $recipts->bank_id = $bankAccount->id;
                        $recipts->referance = $challan->challan_type;
                        $recipts->receive_type = $all_data[11];
                        $recipts->received_by = Auth::user()->id;
                        $recipts->owned_by = $challan->owned_by;
                        $recipts->created_by = \Auth::user()->creatorId();
                        $recipts->save();
                        $recipts->created_at = date('Y-m-d H:i:s', strtotime($all_data[2]));
                        $recipts->updated_at = date('Y-m-d H:i:s', strtotime($all_data[2]));
                        $recipts->save();
                        if ($recipts) {
                            $challan->status = 'paid';
                            $challan->paid_date = date('Y-m-d', strtotime($all_data[2]));
                            $challan->save();
                            $challan_head = ChallanHead::where('head_id', $trns_fee_head->id)->where('challan_id', $challan->id)->first();
                            $challan_head->paid = @$challan_head->paid + intval($all_data[7]);
                            $challan_head->save();

                            $data['id'] = $challan->id;
                            $data['no'] = $challan->challanNo;
                            $data['date'] = $challan->paid_date;
                            $data['reference'] = $challan->reference;
                            $data['description'] = $challan->description;
                            $data['user_id'] = $challan->student_id;
                            $data['user_type'] = 'Student';
                            $data['amount'] = $challan->amount;
                            $data['category'] = $challan->challan_type;
                            $data['owned_by'] = $challan->owned_by;
                            $data['created_by'] = \Auth::user()->creatorId();
                            $data['account_id'] = $bankAccount->chart_account_id;
                            $data['recipt'] = $recipts->id;
                            $data['items'] = $item;
                            $data['total'] = $all_data[7];
                            // dd($data);
                            // $dataret = Utility::brv_entry($data);
                            if (ucwords($all_data[11]) == 'CD') {
                                $dataret = Utility::crv_entry($data);
                            } else {
                                $dataret = Utility::brv_entry($data);
                            }

                        }
                        $transfer->challan_id = $challan->id;
                        $transfer->save();
                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();

                // Generate error file if there are errors
                if (!empty($skip_data)) {
                    $export_filename = 'transfer_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');

                    // Write the header for the error file
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);

                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }

                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }

                // If no errors, return success message
                return redirect()->back()->with('message', "{$success_counter} Student Transfer(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            \Log::error("Transfer Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function RegularChallanImport($file, $request)
    {
        set_time_limit(0);
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;
                //index 1 => BRANCH name
                //index 3 => challan no 
                //index 4 => roll no
                //index 5 => student name
                //index 6 => class name
                //index 7 => billing month 
                //index 8 => class month fee
                //index 9 => admission head amount
                //index 10 => annual head amount
                //index 11 => tuition total amount
                //index 12 => tuition discounted amount
                //index 13 => tuition paid amount
                //index 14 => Computer haed amount
                //index 15 => SECURITY HEAD amount
                //index 16 => Security Adjustemnt
                //index 17 => readamission head amount
                //index 18 => monthly care head amount
                //index 19 => AC head amount
                //index 20 => extra care fee
                //index 21 => Monthly care head amount
                //index 22 => stationary head amount
                //index 23 => study pack amount
                //index 24 => late fee charges
                //index 25 => transport charges
                //index 26 => Arrears
                //index 27 => Net Receivable
                //index 28 => Discount
                //index 29 => Category
                //index 30 => RegId
                while (($all_data = fgetcsv($handle, 7000, ",")) !== FALSE) {
                    if ($count > 0) {
                        $dates = $all_data[7];
                        $billingMonth = array_values(array_filter(explode(', ', $dates)))[0];
                        $otherMonthCount = count(array_values(array_filter(explode(', ', $dates))));
                        $record_status = 'Error';
                        $reason = '';
                        $all_data = array_map('trim', $all_data);
                        $branch = User::where('name', 'like', '%' . $all_data[1] . '%')->first();
                        if (!$branch) {
                            $reason = 'Branch not found: ' . $all_data[1];
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        // class
                        $class = Classes::where('name', 'like', '%' . $all_data[6] . '%')->where('owned_by', $branch->id)->first();
                        if (!$class) {
                            $class = new Classes();
                            $class->name = $all_data[6];
                            $class->owned_by = $branch->id;
                            $class->created_by = auth()->user()->id;
                            $class->save();
                        }
                        if (!empty($all_data[30])) {
                            $regclean = preg_replace('/\s*\(.*?\)/', '', $all_data[30]);
                            $rollclean = preg_replace('/\s*\(.*?\)/', '', $all_data[4]);
                            $reg = StudentRegistration::where('reg_no', $regclean)->where('roll_no', $rollclean)->first();
                            if (!$reg) {
                                dd('Student not found', $all_data, $count, 'rollno ' . $rollclean, 'regno ' . $regclean);
                            }
                        } else {
                            dd('registration number empty', $all_data, $count);
                        }
                        $enr = StudentEnrollments::where('enrollId', $reg->roll_no)->first();
                        if (!$enr) {
                            dd('enrollment not found', $all_data, $count);
                        }
                        // if (!$enr) {
                        //     $reg = StudentRegistration::where('reg_no', $all_data[30])
                        //         // ->where('stdname', 'like', '%' . $all_data[5] . '%')
                        //         ->first();
                        //     if (!$reg) {
                        //         $reason = 'Registration not found: ' . $all_data[30];
                        //         $error_counter++;
                        //         $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        //         $count++;
                        //         continue;
                        //     }
                        //     $enr = StudentEnrollments::where('regId', $reg->id)->first();
                        //     if ($enr) {
                        //         $enr->enrollId = $all_data[4];
                        //         $enr->save();
                        //         $reg->roll_no = $all_data[4];
                        //         if ($reg->student_status != 'withdrawl' && $reg->student_status != 'Enrolled') {
                        //             $reg->student_status = 'other';
                        //         }
                        //         $reg->save();
                        //     } else {
                        //         $enr = new StudentEnrollments();
                        //         $enr->adm_date = date('Y-m-d', strtotime($billingMonth));
                        //         $enr->class_id = $reg->class_id;
                        //         $enr->session_id = $reg->session_id;
                        //         $enr->adm_session = $reg->session_id;
                        //         $enr->adm_branch = $branch->id;
                        //         $enr->enrollId = $all_data[4];
                        //         $enr->regId = $reg->id;
                        //         $enr->active_status = 0;
                        //         $enr->owned_by = $branch->id;
                        //         $enr->created_by = \Auth::user()->creatorId();
                        //         $enr->save();
                        //         $reg->roll_no = $all_data[4];
                        //         if ($reg->student_status != 'withdrawl' && $reg->student_status != 'Enrolled') {
                        //             $reg->student_status = 'other';
                        //         }
                        //         $reg->save();
                        //     }
                        // } else {
                        //     $reg = StudentRegistration::where('roll_no', $all_data[4])->first();
                        //     if ($reg) {
                        //         $enr->regId = $reg->id;
                        //         $enr->save();
                        //         $reg->roll_no = $all_data[4];
                        //         $reg->reg_no = $all_data[30];
                        //         if ($reg->student_status != 'withdrawl' && $reg->student_status != 'Enrolled') {
                        //             $reg->student_status = 'other';
                        //         }
                        //         $reg->save();
                        //     }
                        // }

                        $timestamp = strtotime($billingMonth);
                        $year = date('Y', $timestamp);
                        $month = date('m', $timestamp);
                        $challan = Challans::where('student_id', $reg->id)
                            ->where('challan_type', 'Regular')
                            ->whereYear('challan_date', $year)
                            ->whereMonth('challan_date', $month)
                            ->exists();

                        if ($challan) {
                            dd('challan exist',$count,$all_data);
                            $reason = 'Challan already exists for student: ' . $reg->id;
                            $duplication_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        if ($otherMonthCount > 1) {
                            $advance = Challans::where('student_id', $reg->id)
                                ->where('challan_type', 'Advance')
                                ->whereYear('challan_date', $year)
                                ->whereMonth('challan_date', $month)
                                ->exists();
                            if ($advance) {
                                dd('advance exist');
                                $reason = 'Advance challan already exists for student: ' . $reg->id;
                                $duplication_counter++;
                                $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                                $count++;
                                continue;
                            }
                        }
                        $challan = new Challans();
                        $challan->student_id = $reg->id;
                        $challan->rollno = $reg->roll_no;
                        $challan->class_id = $class->id;
                        $challan->challanNo = $all_data[3];
                        $challan->challan_date = date('Y-m-d', strtotime($billingMonth));
                        $challan->fee_month = date('Y-m-d', strtotime($billingMonth));
                        $challan->challan_type = 'Regular';
                        $challan->concession_amount = $all_data[12];
                        $challan->total_amount = $all_data[27] + $all_data[28];
                        $challan->paid_amount = 0;
                        $challan->issue_date = date('Y-m-d', strtotime($billingMonth));
                        if ($otherMonthCount > 1) {
                            $months = explode(',', $all_data[7]);
                            $formattedMonths = [];
                            foreach ($months as $month) {
                                $timestamp = strtotime('01-' . trim($month));
                                if ($timestamp) {
                                    $formattedMonths[] = date('Y-m-d', $timestamp);
                                }
                            }
                            $challan->other_months = implode(',', $formattedMonths);
                            $challan->challan_type = 'Advance';
                        }
                        $challan->due_date = date('Y-m-d', strtotime($billingMonth . ' +7 days'));
                        $challan->status = 'issued';
                        $challan->session_id = $reg->session_id;
                        $challan->owned_by = $branch->id;
                        $challan->created_by = \Auth::user()->creatorId();
                        $challan->save();
                        $challan->created_at = date('Y-m-d H:i:s', strtotime($billingMonth));
                        $challan->updated_at = date('Y-m-d H:i:s', strtotime($billingMonth));
                        $challan->save();
                        // dd($challan, $all_data);
                        $fee_head_mappings = [
                            ['index' => 9, 'type' => 'admission'],        // Admission fee
                            ['index' => 11, 'type' => 'tuition'],         // Tuition fee
                            ['index' => 18, 'type' => 'monthly care'],   // Monthly care
                            ['index' => 19, 'type' => 'ac'],
                            ['index' => 15, 'type' => 'security'],
                            ['index' => 16, 'type' => 'security adjustment'],
                            ['index' => 14, 'type' => 'computer'],            // AC head
                            ['index' => 17, 'type' => 'readmission'],            // AC head
                            ['index' => 20, 'type' => 'extra care'],     // Extra care
                            ['index' => 22, 'type' => 'stationary'],     // Stationary
                            ['index' => 23, 'type' => 'study pack'],
                            ['index' => 24, 'type' => 'late fee'],
                            ['index' => 25, 'type' => 'transport']
                        ];
                        $total_fees = 0;
                        $item = [];
                        $itemIndex = 0;
                        $fee_head_mappings = array_filter(array_map(function ($mapping) use ($all_data, &$total_fees) {
                            $index = $mapping['index'];

                            if (isset($all_data[$index]) && is_numeric($all_data[$index]) && $all_data[$index] > 0) {
                                $amount = $all_data[$index];
                                $mapping['amount'] = $amount;
                                $total_fees += $amount;
                                return $mapping;
                            }
                            return null;
                        }, $fee_head_mappings));            
                        $totalReceivedAmnt = 0;
                        foreach ($fee_head_mappings as $mapping) {
                            $index = $mapping['index'];
                            $type = $mapping['type'];
                            $fee_head = FeeHead::where('fee_head', 'LIKE', '%' . $type . '%')->first();
                            if ($fee_head) {
                                $head_amount = $mapping['amount'];
                                $concession = 0;
                                if ($type == 'tuition' && isset($all_data[12])) {
                                    // dd($all_data);
                                    $concession = $all_data[12];
                                    // $head_amount += $concession;
                                }
                                $totalReceivedAmnt += $head_amount;
                                // dd('out');
                                $challan_head = new ChallanHead();
                                $challan_head->challan_id = $challan->id;
                                $challan_head->head_id = $fee_head->id;
                                $challan_head->price = $head_amount;
                                $challan_head->concession = $concession;
                                $challan_head->save();
                                $challan_head->created_at = date('Y-m-d H:i:s', strtotime($billingMonth));
                                $challan_head->updated_at = date('Y-m-d H:i:s', strtotime($billingMonth));
                                $challan_head->save();
                                //add discount item as well
                                $item[$itemIndex]['prod_id'] = $challan_head->id;
                                $item[$itemIndex]['head'] = $fee_head->id;
                                $item[$itemIndex]['price'] = $head_amount ? $head_amount : 0;
                                $item[$itemIndex]['quantity'] = 1;
                                $item[$itemIndex]['concession'] = $concession;
                                $item[$itemIndex]['total'] = $head_amount;
                                $itemIndex++;
                            }
                        }
                        // if ($count == 176) {
                        //     dd($totalReceivedAmnt, $all_data,$item);
                        // }
                        // dd($totalReceivedAmnt);
                        $challan->total_amount = $totalReceivedAmnt;
                        $challan->save();
                        // dd($item, $challan,$enr);
                        $data['id'] = $challan->id;
                        $data['no'] = $challan->challanNo;
                        $data['date'] = $challan->challan_date;
                        $data['reference'] = $challan->student_id;
                        $data['category'] = 'Regular';
                        $data['user_id'] = $reg->roll_no;
                        $data['std_name'] = @$reg->stdname ?? '';
                        $data['branch_name'] = $branch->name;
                        $data['fee_month'] = date('M-y', strtotime($challan->fee_month));
                        $data['user_type'] = 'Student';
                        $data['owned_by'] = $challan->owned_by;
                        $data['created_by'] = $challan->created_by;
                        $data['created_at'] = date('Y-m-d H:i:s', strtotime($billingMonth));
                        $data['updated_at'] = date('Y-m-d H:i:s', strtotime($billingMonth));
                        $data['items'] = $item;
                        $dataret = Utility::jrentry($data);
                        $challan->voucher_id = $dataret;
                        $challan->save();
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'regular_challan_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Regular Challan(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            \Log::error("Regular Challan Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function SecurityChallanImport($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';

                        // Check for empty data in first column
                        if (empty($all_data[0])) {
                            $reason = 'Empty enrollment ID';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Validate enrollment
                        $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
                        if (!$enr) {
                            $reason = 'Enrollment not found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        // Check for existing security challan (prevent duplicates)
                        $securityChallan = Challans::where('student_id', $enr->enrollId)
                            ->where('challan_type', 'Security')
                            ->first();
                        if ($securityChallan) {
                            $reason = 'Security challan already exists for student: ' . $enr->enrollId;
                            $duplication_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'security_challan_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Security Challan(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error("Security Challan Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function ReceiptsImport($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;

                while (($all_data = fgetcsv($handle, 30000, ",")) !== FALSE) {
                    if ($count > 0) {
                        // if($count == 2){
                        //     dd($all_data);
                        // }
                        $record_status = 'Error';
                        $reason = '';
                        // dd($all_data);
                        // Check for empty data in first column
                        // 0 index => branch name
                        // 1 index => rpt. date
                        // 2 index => ch type
                        // 3 index => rollno
                        // 4 index => student name
                        // 5 index => class
                        // 6 index => challan no
                        // 7 index => biling period
                        // 8 index => fee subs
                        // 9 index => bank
                        // 10 index => D status
                        // 11 index => T.head
                        // 12 index => Reference
                        // 13 index => Amount
                        // 14 index => Over receipt
                        if (empty($all_data[6])) {
                            $reason = 'Empty Challan No';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }

                        $chaln = Challans::with('heads')->where('challanNo', $all_data[6])->first();
                        if (!$chaln) {
                            $reason = 'Challan Not Exist';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $branch = User::where('name', 'like', '%' . $all_data[0] . '%')->first();
                        if (!$branch) {
                            dd($all_data, 'branch');
                            $reason = 'Branch Not Found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $remainingAmount = $all_data[13];
                        $challanBeforePaid = $chaln->total_amount - ($chaln->paid_amount + $chaln->concession_amount);
                        if ($remainingAmount <= 0) {
                            $reason = 'Amount is 0';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        //challan already paid.
                        if (($chaln->paid_amount + $chaln->concession_amount) == $chaln->total_amount) {
                            $reason = 'Challan Already Paid';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $itemIndex = 0;
                        $item = [];
                        foreach ($chaln->heads as $head) {
                            if (($head->price - ($head->concession ?? 0)) == $head->paid) {
                                continue;
                            }
                            $dueAmount = $head->price - ($head->concession ?? 0);
                            $alreadyPaid = $head->paid ?? 0;
                            $remainingDue = $dueAmount - $alreadyPaid;

                            if ($remainingDue <= 0) {
                                continue;
                            }

                            if ($remainingAmount >= $remainingDue) {
                                $head->paid += $remainingDue;
                                $chaln->paid_amount += $remainingDue;
                                $remainingAmount -= $remainingDue;
                            } else {
                                $head->paid += $remainingAmount;
                                $chaln->paid_amount += $remainingAmount;
                                $remainingAmount = 0;
                                break;
                            }
                            $head->save();
                            // dd($head);
                            $item[$itemIndex]['head'] = $head->head_id;
                            $item[$itemIndex]['price'] = $head->paid;
                            $item[$itemIndex]['quantity'] = 1;
                            $item[$itemIndex]['concession'] = 0;
                            $item[$itemIndex]['total'] = $head->paid;
                            $itemIndex++;
                        }
                        // After distributing to all heads
                        if ($remainingAmount > 0) {
                            $latefeeHead = FeeHead::where('fee_head', 'like', '%Late Fee%')->first();
                            // dd($latefeeHead);
                            $latehead = new ChallanHead();
                            $latehead->challan_id = $chaln->id;
                            $latehead->head_id = $latefeeHead->id;
                            $latehead->price = $remainingAmount;
                            $latehead->concession = 0;
                            $latehead->paid = $remainingAmount;
                            $latehead->save();

                            $chaln->total_amount += $remainingAmount;
                            $chaln->paid_amount += $remainingAmount;
                            $chaln->save();
                            if ($latehead) {
                                // dd($count,$all_data,$chaln,$remainingAmount);
                                $account_name = ChartOfAccount::where('id', $latefeeHead->account_id)->first();
                                $feeHeads = FeeHead::where('id', $latefeeHead->id)->first();
                                $journalItem = new JournalItem();
                                $journalItem->journal = @$chaln->voucher_id;
                                $journalItem->account = @$feeHeads->account_id;
                                $journalItem->head = @$latefeeHead->id;
                                $journalItem->entry_id = @$latehead->id;
                                $journalItem->description = 'Income Account: Roll no ' . $all_data[3] . ' Challan no ' . $all_data[6] . ' - ' . @$data['std_name'] . ' - ' . date('M-y', strtotime($all_data[7])) . ' - ' . @$all_data[0];
                                $journalItem->types = 'Challan';
                                $journalItem->credit = $remainingAmount;
                                $journalItem->debit = 0;
                                $journalItem->save();
                                $journalItem->created_at = @$chaln->created_at;
                                $journalItem->updated_at = @$chaln->updated_at;
                                $journalItem->save();
                                //  reciveable entry
                                $journalItem = new JournalItem();
                                $journalItem->journal = @$chaln->voucher_id;
                                $journalItem->account = @$feeHeads->receivable_account_id;
                                $journalItem->head = @$latefeeHead->id;
                                $journalItem->description = 'Account Receivable: Roll no ' . $data['user_id'] . ' Challan no ' . $data['no'] . ' - ' . @$data['std_name'] . ' - ' . @$data['fee_month'] . ' - ' . @$data['branch_name'];
                                $journalItem->entry_id = @$latehead->id;
                                $journalItem->types = 'Challan';
                                $journalItem->credit = 0;
                                $journalItem->debit = $remainingAmount;
                                $journalItem->save();
                                $journalItem->created_at = @$chaln->created_at;
                                $journalItem->updated_at = @$chaln->updated_at;
                                $journalItem->save();
                            }
                            $item[$itemIndex]['head'] = $latehead->head_id;
                            $item[$itemIndex]['price'] = $remainingAmount;
                            $item[$itemIndex]['quantity'] = 1;
                            $item[$itemIndex]['concession'] = 0;
                            $item[$itemIndex]['total'] = $remainingAmount;
                            $itemIndex++;
                        }

                        //bank account on name basis
                        //cleen bank name
                        $clean_title = trim(preg_replace('/\s*\(.*?\)/', '', $all_data[9]), " \t\n\r\0\x0B-");
                        // dd($clean_title);
                        $bankAccount = BankAccount::where('account_number', $clean_title)->first();
                        // dd($bankAccount);
                        if (!$bankAccount) {
                            dd($all_data, 'bank not found', $count);
                            $reason = 'Bank Account Not Found';
                            $error_counter++;
                            $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $recipts = new StudentReceipt();
                        $recipts->recipt_date = date('Y-m-d', strtotime($all_data[1]));
                        $recipts->challan_id = $chaln->id;
                        $recipts->recipt_amount = $all_data[13];
                        $recipts->student_id = $chaln->student_id;
                        $recipts->challan_amount = $challanBeforePaid;
                        $recipts->late_amount = 0;
                        $recipts->arrears = 0;
                        $recipts->bank_id = $bankAccount->id;
                        $recipts->account_id = $bankAccount->chart_account_id;
                        $recipts->referance = $all_data[12];
                        $recipts->receive_type = $all_data[2];
                        $recipts->received_by = $branch->id;
                        $recipts->owned_by = $branch->id;
                        $recipts->created_by = \Auth::user()->creatorId();
                        $recipts->save();
                        $recipts->created_at = date('Y-m-d H:i:s', strtotime($all_data[1]));
                        $recipts->updated_at = date('Y-m-d H:i:s', strtotime($all_data[1]));
                        $recipts->save();
                        // dd($recipts);
                        if ($recipts) {
                            if (($chaln->paid_amount + $chaln->concession_amount) >= $chaln->total_amount) {
                                $chaln->status = 'paid';
                            } else {
                                $chaln->status = 'partial';
                            }
                            $chaln->paid_date = date('Y-m-d', strtotime($all_data[1]));
                            $chaln->save();
                            $data['id'] = $chaln->id;
                            $data['no'] = $chaln->challanNo;
                            $data['bank_id'] = $bankAccount->id;
                            $data['date'] = $chaln->paid_date;
                            $data['reference'] = $all_data[12];
                            $data['description'] = $chaln->description;
                            $data['user_id'] = $chaln->student_id;
                            $data['user_type'] = 'Student';
                            $data['amount'] = $all_data[13];
                            $data['category'] = $chaln->challan_type;
                            $data['owned_by'] = $branch->id;
                            $data['branch_name'] = $branch->name;
                            $data['std_name'] = $all_data[4];
                            $data['bank_name'] = $bankAccount->bank_name;
                            $data['created_by'] = \Auth::user()->creatorId();
                            $data['account_id'] = $bankAccount->chart_account_id;
                            $data['recipt'] = $recipts->id;
                            $data['items'] = $item;
                            $data['total'] = $all_data[13];
                            // $dataret = Utility::brv_entry($data);

                            if (ucwords($all_data[2]) == 'CD') {
                                $dataret = Utility::crv_entry($data);
                            } else {
                                $dataret = Utility::brv_entry($data);
                            }

                        }
                        $chaln->save();
                        // if ($receipt) {
                        //     $reason = 'Receipt already exists for this challan';
                        //     $duplication_counter++;
                        //     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        //     $count++;
                        //     continue;
                        // }
                        // if (!$feeHead) {
                        //     $reason = 'Fee Head Not Found' . $all_data[11];
                        //     $error_counter++;
                        //     $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                        //     $count++;
                        //     continue;
                        // }
                        // if ($feeHead->id != $chaln->heads[0]->head_id) {

                        // }

                        // Add to successful records
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        // Save the header row with additional columns
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                    Session::put('counter', $success_counter);
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'receipts_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                Session::forget('counter');
                return redirect()->back()->with('message', "{$success_counter} Receipt(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e, $count, $all_data);
            \Log::error("Receipts Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }
    private function challanNo()
    {
        $latest = Challans::where('created_by', '=', \Auth::user()->creatorId())->orderBY('id', 'desc')->latest()->first();
        if (!$latest) {
            return 1;
        }
        return $latest->challanNo + 1;
    }
    private function ConcessionImport($file, $request)
    {
        set_time_limit(0);
        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);

        // Initialize counters and tracking arrays
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;
                while (($all_data = fgetcsv($handle, 500, ",")) !== FALSE) {
                    if ($count > 0) {
                        $record_status = 'Error';
                        $reason = '';
                        $concessionsheads = explode('+', $all_data[1]);
                        $parsedConcessions = [];
                        foreach ($concessionsheads as $concession) {
                            if (preg_match('/^(\d+)%(.+)$/', $concession, $matches)) {
                                $percentage = (int) $matches[1];
                                $fullHeadName = trim($matches[2]);
                                $headName = preg_replace('/\([^)]+\)/', '', $fullHeadName);
                                $headName = trim($headName);
                                $feeHead = FeeHead::where(DB::raw('LOWER(fee_head)'), 'LIKE', strtolower($headName) . '%')->first();
                                $parsedConcessions[] = [
                                    'percentage' => $percentage,
                                    'head_name' => $headName,
                                    'head' => @$feeHead,
                                ];
                            }
                        }
                        // dd($parsedConcessions);
                        $concession_policy = new ConcessionPolicy();
                        $concession_policy->order_no = $all_data[0];
                        $concession_policy->title = $all_data[1];
                        $concession_policy->description = $all_data[1];
                        $concession_policy->owned_by = \Auth::user()->ownedId();
                        $concession_policy->created_by = \Auth::user()->creatorId();
                        $concession_policy->save();
                        foreach ($parsedConcessions as $concession) {
                            if (!$concession['head'] || $concession['head'] == null) {
                                $reason = 'Fee Head Not Found: ' . $concession['head_name'];
                                $error_counter++;
                                $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
                                $count++;
                                continue;
                            }
                            $concession_policy_head = new ConcessionPolicyHead();
                            $concession_policy_head->concession_id = $concession_policy->id;
                            $concession_policy_head->head_id = $concession['head']->id;
                            $concession_policy_head->percentage = $concession['percentage'];
                            $concession_policy_head->save();
                        }
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'concession_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Concession(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            \Log::error("Concession Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }

    //    private function ConcessionListImport($file, $request)
//     {
//         set_time_limit(0);
//         $file = $request->file('excel_file');
//         $filename = $file->getClientOriginalName();
//         $file->move(public_path('assets/import/csv_file/'), $filename);
//         $filepath = public_path('assets/import/csv_file/' . $filename);

    //         $success_counter = 0;
//         $error_counter = 0;
//         $duplication_counter = 0;
//         $skip_data = [];
//         $processed_records = [];
//         DB::beginTransaction();
//         try {
//             if (($handle = fopen($filepath, 'r')) !== FALSE) {
//                 $count = 0;
//                 while (($all_data = fgetcsv($handle, 500, ",")) !== FALSE) {
//                     if ($count > 0) {
//                         $record_status = 'Error';
//                         $reason = '';
//                         //12
//                         $active_status = strtolower($all_data[13]) == 'active' ? 1 : 0;
//                         // Check for empty data in first column
//                         if (empty($all_data[8])) {
//                             $reason = 'Concession Not Found';
//                             $error_counter++;
//                             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                             $count++;
//                             continue;
//                         }
//                         $order_no = explode(' - ', $all_data[8]);
//                         if (!$order_no) {
//                             $reason = 'Order No Not Found: ' . $all_data[8];
//                             $duplication_counter++;
//                             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                             $count++;
//                             continue;
//                         }
//                         // dd($order_no);
//                         $concession_policy = ConcessionPolicy::with('policy_head')->where('order_no', $order_no[0])->first();
//                         if (!$concession_policy) {
//                             $reason = 'Concession Policy Not Found: ' . $all_data[8];
//                             $error_counter++;
//                             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                             $count++;
//                             continue;
//                         }
//                         $stdconcession = Concession::where('student_id', $all_data[3])->where('concession_id', $concession_policy->id)->where('active_status', $active_status)->first();
//                         if ($stdconcession) {
//                             $reason = 'Concession already exists for student: ' . $all_data[1];
//                             $duplication_counter++;
//                             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                             $count++;
//                             continue;
//                         }

    //                         if (empty($all_data[3])) {  // Changed from $all_data[3] == '' to empty() for better null/empty checking
//                             // Registration student lookup
//                             $student = StudentRegistration::where('reg_no', $all_data[2])->first();  // Changed from [3] to [2] as it seems you're checking reg_no
//                             if (!$student) {
//                                 $reason = 'Student Not Found reg no: ' . $all_data[2];
//                                 $error_counter++;
//                                 $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                                 $count++;
//                                 continue;
//                             }
//                             $student_type = 'Registration Student';
//                         } else {
//                             // Enrolled student lookup
//                             $a = StudentEnrollments::where('enrollId', $all_data[3])->first();
//                             if (!$a) {
//                                 $reason = 'Student Not Found with roll no: ' . $all_data[3];
//                                 $error_counter++;
//                                 $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                                 $count++;
//                                 continue;
//                             }else{
//                                 $student = StudentRegistration::where('id', $a->regId)->first();
//                             }
//                             $student_type = 'Regular Student';
//                         }
//                          $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[0]);
//                         $branchfrom = User::where('name', 'like', '%' . $clean_title . '%')->first();
//                         if (!$branchfrom) {
//                             dd($all_data, 'branchfrom', $clean_title);
//                             $reason = 'From branch not found: ' . $all_data[0];
//                             $error_counter++;
//                             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                             $count++;
//                             continue;
//                         }
//                         $cls = Classes::where('name', $all_data[4])->where('owned_by', $branchfrom->id)->first();
//                         if (!$cls) {
//                             $reason = 'Class Not Found: ' . $all_data[10];
//                             $error_counter++;
//                             $skip_data[] = array_merge($all_data, ['Status' => $record_status, 'Reason' => $reason]);
//                             $count++;
//                             continue;
//                         }
//                         $concession = new Concession();
//                         $concession->student_id = $student->id;
//                         $concession->class_id = $cls->id;
//                         $concession->concession_id = $concession_policy->id;
//                         $concession->concession_by = $all_data[11];
//                         $concession->apply_date = date('Y-m-d', strtotime($all_data[6]));
//                         $concession->start_date = date('Y-m-d', strtotime($all_data[6]));
//                         $concession->end_date = date('Y-m-d', strtotime($all_data[9]));
//                         $concession->status = $all_data[12];
//                         $concession->remarks = $student_type;
//                         $concession->active_status = $active_status;
//                         $concession->created_by = \Auth::user()->creatorId();
//                         $concession->owned_by = $student->owned_by;
//                         $concession->save();
//                         $concession->created_at = date('Y-m-d', strtotime($all_data[6]));
//                         $concession->updated_at = date('Y-m-d', strtotime($all_data[6]));
//                         $concession->save();
//                         $success_counter++;
//                     }
//                     $count++;
//                 }
//                 fclose($handle);
//                 DB::commit();
//                 if (!empty($skip_data)) {
//                     $export_filename = 'concession_import_errors_' . time() . '.csv';
//                     $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
//                     $error_file = fopen($error_filepath, 'w+');
//                     fputcsv($error_file, ['Enrollment ID', 'Registration No', 'Student Name', 'Status', 'Reason']);
//                     foreach ($skip_data as $row) {
//                         fputcsv($error_file, $row);
//                     }
//                     fclose($error_file);
//                     return response()->download($error_filepath)->deleteFileAfterSend(true);
//                 }
//                 return redirect()->back()->with('message', "{$success_counter} Concession(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
//             }
//         } catch (\Exception $e) {
//             DB::rollBack();
//             dd($e);
//             \Log::error("Concession Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
//             return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
//         }
//     }

    private function ConcessionListImport($file, $request)
    {
        set_time_limit(0);
        $filename = $file->getClientOriginalName();
        $file->move(public_path('assets/import/csv_file/'), $filename);
        $filepath = public_path('assets/import/csv_file/' . $filename);
        $success_counter = 0;
        $error_counter = 0;
        $duplication_counter = 0;
        $skip_data = [];
        $processed_records = [];

        DB::beginTransaction();
        try {
            if (($handle = fopen($filepath, 'r')) !== FALSE) {
                $count = 0;
                //index 0 => Branch
                //index 1 => student name
                //index 2 => roll no
                //index 3 => class name
                //index 4 => section name
                //index 6 => d.o.j 

                while (($all_data = fgetcsv($handle, 7000, ",")) !== FALSE) {
                    if ($count > 0) {

                        // $dates = $all_data[4];
                        // $month_count = count(array_values(array_filter(explode(', ', $dates))));
                        // $billingMonths = array_values(array_filter(explode(', ', $dates)));
                        $record_status = 'Error';
                        $reason = '';
                        $all_data = array_map('trim', $all_data);
                        $enr = StudentEnrollments::where('enrollId', $all_data[2])->first();
                        if (!$enr) {
                            $reason = 'Enrollment not found: ' . $all_data[2];
                            $error_counter++;
                            $skip_data[] = array_merge([$all_data[0], $all_data[1], $all_data[2]], ['Status' => $record_status, 'Reason' => $reason]);
                            $count++;
                            continue;
                        }
                        $student = StudentRegistration::where('id', $enr->regId)->first();
                        if ($student) {
                            if ($all_data[39] != 'Discount Policy') {

                                $parsedConcessions = [];

                                $policyString = $all_data[39]; // Example: '15%T.FEE+15%ADM+50%SECURITY'

                                $headNameMappings = [
                                    'T.FEE' => 'TUITION',
                                    'TUT' => 'TUITION',
                                    'TUITION' => 'TUITION',
                                    // add more mappings as needed
                                ];

                                $policyParts = explode('+', $policyString);
                                $extractedHeads = [];

                                foreach ($policyParts as $part) {
                                    if (preg_match('/^([\d.]+)%(.+)$/', trim($part), $matches)) {
                                        $percentage = (float) $matches[1];
                                        $fullHeadName = trim($matches[2]);
                                        $headName = preg_replace('/\([^)]+\)/', '', $fullHeadName);
                                        $headName = trim($headName);
                                        $cleanHeadName = strtoupper($headName);

                                        if (array_key_exists($cleanHeadName, $headNameMappings)) {
                                            $headName = $headNameMappings[$cleanHeadName];
                                        }

                                        $feeHead = FeeHead::whereRaw('LOWER(fee_head) LIKE ?', [strtolower($headName) . '%'])->first();

                                        if ($feeHead) {
                                            $extractedHeads[] = [
                                                'head_id' => $feeHead->id,
                                                'percentage' => $percentage
                                            ];
                                        }
                                    }
                                }

                                $matchingPolicies = ConcessionPolicy::with('policy_head') // Assuming relation to pivot table
                                    ->get()
                                    ->filter(function ($policy) use ($extractedHeads) {

                                        // Step 2.1: Compare total number of heads
                                        if (count($policy->policy_head) !== count($extractedHeads)) {
                                            return false;
                                        }

                                        foreach ($extractedHeads as $extractedHead) {
                                            $match = $policy->policy_head->firstWhere('head_id', $extractedHead['head_id']);

                                            // Head not found or percentage does not match
                                            if (!$match || $match->percentage != $extractedHead['percentage']) {
                                                return false;
                                            }
                                        }

                                        return true; // All heads and percentages matched
                                    });
                                $a = $matchingPolicies->pluck('id')->toArray();
                                // dd($a,$extractedHeads,$policyParts,$policyString);
                                // if no policies found, error throw error
                                if (count($a) == 0) {
                                    // create new policy
                                    $policy = new ConcessionPolicy();
                                    $policy->order_no = 1;
                                    $policy->title = $all_data[39];
                                    $policy->description = $all_data[39];
                                    $policy->owned_by = \Auth::user()->ownedId();
                                    $policy->created_by = \Auth::user()->creatorId();
                                    $policy->save();
                                    foreach ($extractedHeads as $extractedHead) {
                                        $policy_head = new ConcessionPolicyHead();
                                        $policy_head->concession_id = $policy->id;
                                        $policy_head->head_id = $extractedHead['head_id'];
                                        $policy_head->percentage = $extractedHead['percentage'];
                                        $policy_head->save();
                                    }
                                    $a[] = $policy->id;
                                    // $reason = 'Concession Policy not found: ' . $all_data[2];
                                    // $error_counter++;
                                    // $skip_data[] = array_merge([$all_data[0], $all_data[1], $all_data[2], $all_data[39]], ['Status' => $record_status, 'Reason' => $reason]);
                                    // $count++;
                                    // continue;
                                }


                                Concession::where('student_id', $student->id)->update([
                                    'end_date' => '2024-01-01', // Update the end date to 2024-01-01
                                    'active_status' => '0',
                                ]);
                                $con = Concession::where('student_id', $student->id)->wherein('concession_id', $a)->first();
                                // if no concession found, create new concession
                                if (!$con) {
                                    $con = new Concession();
                                    $con->student_id = $student->id;
                                    $con->class_id = $student->class_id;
                                    $con->concession_id = $a[0];
                                    $con->concession_by = 'MOHSIN FIAZ';
                                    $con->apply_date = '2024-01-01';
                                    $con->start_date = '2024-01-01';
                                    $con->end_date = '2038-01-01';
                                    $con->remarks = 'Imported';
                                    $con->status = 'Approved';
                                    $con->owned_by = $student->owned_by;
                                    $con->created_by = \Auth::user()->creatorId();
                                    $con->save();
                                }
                                if (!$con) {
                                    $reason = 'Concession not found: ' . $all_data[2];
                                    $error_counter++;
                                    $skip_data[] = array_merge([$all_data[0], $all_data[1], $all_data[2], $all_data[39]], ['Status' => $record_status, 'Reason' => $reason]);
                                    $count++;
                                    // continue;
                                }
                                if ($con) {
                                    $con->end_date = '2038-01-01';
                                    $con->active_status = '1';
                                    $con->owned_by = $student->owned_by;
                                    $con->save();
                                }
                                // dd($con);
                            }

                            $fee_heads = FeeHead::get();
                            if ($fee_heads) {
                                for ($i = 0; $i < count($fee_heads); $i++) {
                                    $classfee = StudentFeeStructure::updateOrCreate(
                                        [
                                            'reg_id' => $student->id,
                                            'branch_id' => $student->owned_by,
                                            'head_id' => $fee_heads[$i]['id'],
                                        ],
                                        [
                                            'amount' => 0,
                                            'class_id' => $student->class_id,
                                            'discount' => '0', // set discount to 0 for all fee heads
                                            'owned_by' => $student->owned_by,
                                            'created_by' => $student->created_by,
                                        ]
                                    );
                                }
                            }

                            $headadmission = FeeHead::where('fee_head', 'like', '%ADMISSION FEE%')->first();
                            $headadmissionFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headadmission->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[7],
                                    'discount' => !$con ? $all_data[8] : '0',
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[10],
                                ]
                            );
                            $headannual = FeeHead::where('fee_head', 'like', '%ANNUAL FEE%')->first();
                            $headannualFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headannual->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[11],
                                    'class_id' => $student->class_id,
                                    'discount' => !$con ? $all_data[12] : '0',
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[14],
                                ]
                            );
                            $headsecurity = FeeHead::where('fee_head', 'like', '%SECURITY FEE%')->first();
                            $headsecurityFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headsecurity->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[15],
                                    'class_id' => $student->class_id,
                                    'discount' => !$con ? $all_data[16] : '0',
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[18],
                                ]
                            );
                            $headtution = FeeHead::where('fee_head', 'like', '%Tuition Fee%')->first();
                            $headtutionFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headtution->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[19],
                                    'class_id' => $student->class_id,
                                    'discount' => !$con ? $all_data[20] : '0',
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[22],
                                ]
                            );

                            $headac = FeeHead::where('fee_head', 'like', '%AC-INVERTER FEE%')->first();
                            $headacFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headac->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[23],
                                    'class_id' => $student->class_id,
                                    'discount' => $all_data[24],
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[26],
                                ]
                            );
                            $headcarefee = FeeHead::where('fee_head', 'like', '%EXTRA CARE FEE%')->first();
                            $headextraFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headcarefee->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[27],
                                    'class_id' => $student->class_id,
                                    'discount' => $all_data[28],
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[30],
                                ]
                            );
                            $headlatefee = FeeHead::where('fee_head', 'like', '%MONTHLY CARE-LATE STAY FEE%')->first();
                            $headextraFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headlatefee->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[31],
                                    'class_id' => $student->class_id,
                                    'discount' => $all_data[32],
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[34],
                                ]
                            );
                            $headtransportfee = FeeHead::where('fee_head', 'like', '%TRANSPORT FEE%')->first();
                            $headtransportFee = StudentFeeStructure::updateOrCreate(
                                [
                                    'reg_id' => $student->id,
                                    'branch_id' => $student->owned_by,
                                    'head_id' => $headtransportfee->id,
                                ],
                                [
                                    'student_id' => $student->roll_no,
                                    'amount' => $all_data[35],
                                    'class_id' => $student->class_id,
                                    'discount' => $all_data[36],
                                    'owned_by' => $student->owned_by,
                                    'created_by' => $student->created_by,
                                    'checked_status' => $all_data[38],
                                ]
                            );

                        }
                        $processed_records[] = array_merge($all_data, ['Status' => 'Success', 'Reason' => '']);
                        $success_counter++;
                    } else {
                        $header = $all_data;
                        $header[] = 'Status';
                        $header[] = 'Reason';
                        $processed_records[] = $header;
                    }
                    $count++;
                }
                fclose($handle);
                DB::commit();
                if (!empty($skip_data)) {
                    $export_filename = 'regular_challan_import_errors_' . time() . '.csv';
                    $error_filepath = public_path('assets/import/csv_file/' . $export_filename);
                    $error_file = fopen($error_filepath, 'w+');
                    fputcsv($error_file, ['Enrollment ID', 'Roll No', 'Student Name', 'Status', 'Reason']);
                    foreach ($skip_data as $row) {
                        fputcsv($error_file, $row);
                    }
                    fclose($error_file);
                    return response()->download($error_filepath)->deleteFileAfterSend(true);
                }
                return redirect()->back()->with('message', "{$success_counter} Regular Challan(s) added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            \Log::error("Regular Challan Import Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
        }
    }


    //  private function WithdrawImport($file, $request)
    // {
    //     set_time_limit(0);
    //     $file = $request->file('excel_file');
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // Initialize counters and error tracking
    //     $success_counter = 0;
    //     $error_counter = 0;
    //     $duplication_counter = 0;
    //     $error_records = [];

    //     DB::beginTransaction();
    //     try {
    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             $count = 0;

    //             while (($all_data = fgetcsv($handle, 3500, ",")) !== FALSE) {
    //                 if ($count > 0) {
    //                     // Check for empty data in first column
    //                     if (empty($all_data[0])) {
    //                         $error_records[] = ['row' => $count, 'reason' => 'Empty enrollment ID'];
    //                         $error_counter++;
    //                         $count++;
    //                         continue;
    //                     }
    //                     $clean_title = preg_replace('/\s*\(.*?\)/', '', $all_data[4]);
    //                     $branchfrom = User::where('name', $clean_title)->first();
    //                     if (!$branchfrom) {
    //                         $error_records[] = ['row' => $count, 'reason' => 'From branch not found: ' . $all_data[4]];
    //                         $error_counter++;
    //                         $count++;
    //                         continue;
    //                     }
    //                     // Validate enrollment
    //                     $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
    //                     if (!$enr) {
    //                         if (!empty($all_data[5]) || !empty($all_data[6])) {
    //                             $reg = StudentRegistration::where('stdname', 'LIKE', '%' . $all_data[5] . '%')
    //                                 ->orWhere('fathername', 'LIKE', '%' . $all_data[6] . '%')
    //                                 ->where('owned_by', $branchfrom->id)
    //                                 ->first();
    //                             // dd($all_data, $enr,$reg);
    //                             if ($reg) {
    //                                 // If registration found by name, try to find the enrollment
    //                                 $enr = StudentEnrollments::where('regId', $reg->reg_no)->where('owned_by', $branchfrom->id)->first();
    //                                 if (!$enr) {
    //                                     $error_records[] = ['row' => $count, 'reason' => 'Enrollment not found for student: ' . $all_data[5]];
    //                                     $error_counter++;
    //                                     $count++;
    //                                     continue;
    //                                 }
    //                             } else {
    //                                 $error_records[] = ['row' => $count, 'reason' => 'Student not found: ' . $all_data[5] . ' ' . $all_data[6]];
    //                                 $error_counter++;
    //                                 $count++;
    //                                 continue;
    //                             }
    //                         } else {
    //                             $error_records[] = ['row' => $count, 'reason' => 'Enrollment not found: ' . $all_data[0]];
    //                             $error_counter++;
    //                             $count++;
    //                             continue;
    //                         }
    //                     }
    //                     // Check for existing withdrawal (prevent duplicates)
    //                     $withdraw = StudentWithdrawal::where('student_id', $enr->enrollId)->first();
    //                     if ($withdraw) {
    //                         $error_records[] = ['row' => $count, 'reason' => 'Student already withdrawn'];
    //                         $duplication_counter++;
    //                         $count++;
    //                         continue;
    //                     }

    //                     // Validate withdrawal date
    //                     $withdrawal_date = date('Y-m-d', strtotime($all_data[1]));
    //                     if (!$withdrawal_date) {
    //                         $error_records[] = ['row' => $count, 'reason' => 'Invalid withdrawal date: ' . $all_data[1]];
    //                         $error_counter++;
    //                         $count++;
    //                         continue;
    //                     }
    //                     if (!$enr->class_id) {
    //                         $reg = StudentRegistration::where('reg_no', $enr->regId)->where('owned_by', $enr->owned_by)->first();
    //                         if (!$reg->class_id) {
    //                             $class = Classes::where('name', $all_data[7])->where('owned_by', $enr->owned_by)->first();
    //                             if (!$class) {
    //                                 $class = new Classes();
    //                                 $class->name = $all_data[7];
    //                                 $class->owned_by = $enr->owned_by;
    //                                 $class->created_by = auth()->user()->id;
    //                                 $class->save();
    //                             }
    //                             $reg->class_id = $class->id;
    //                             $reg->save();
    //                         }
    //                         $enr->class_id = $reg->class_id;
    //                         $enr->save();
    //                     }
    //                     // Create new withdrawal
    //                     $withdrawal = new StudentWithdrawal();
    //                     $withdrawal->student_id = $enr->enrollId;
    //                     $withdrawal->challan_id = null;
    //                     $withdrawal->branch_id = $branchfrom->id;
    //                     $withdrawal->class_id = $enr->class_id;
    //                     $withdrawal->withdraw_date = $withdrawal_date;
    //                     $withdrawal->apply_date = $withdrawal_date;
    //                     $withdrawal->reason = isset($all_data[3]) && $all_data[3] != '' ? $all_data[3] : $all_data[2];
    //                     $withdrawal->remark = isset($all_data[3]) && $all_data[3] != '' ? $all_data[3] : $all_data[2];
    //                     $withdrawal->owned_by = $enr->owned_by;
    //                     $withdrawal->created_by = \Auth::user()->creatorId();
    //                     $withdrawal->save();
    //                     $success_counter++;
    //                 }
    //                 $count++;
    //             }
    //             fclose($handle);
    //             DB::commit();
    //             return redirect()->back()->with('message', "{$success_counter} Student Withdrawal added successfully. {$error_counter} rows skipped due to errors. {$duplication_counter} duplicates found.");
    //         }
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         dd($e);
    //         return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     }
    // }
    // private function StudentDetail($file, $request)
    // {
    //     set_time_limit(0);
    //     $file = $request->file('excel_file');
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);
    //     DB::beginTransaction();
    //     try {
    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             $count = 0;
    //             $success_counter = 0;
    //             $error_counter = 0;
    //             $duplication_counter = 0;
    //             while (($all_data = fgetcsv($handle, 4000, ",")) !== FALSE) {
    //                 if ($count > 0) {
    //                     if (empty($all_data[0])) {
    //                         $error_counter++;
    //                         continue;
    //                     }
    //                     $enr = StudentEnrollments::where('enrollId', $all_data[0])->first();
    //                     if (!$enr) {
    //                         $error_counter++;
    //                         continue;
    //                     }
    //                     $reg = StudentRegistration::where('reg_no', $enr->regId)->where('owned_by', $enr->owned_by)->first();
    //                     if (!$reg) {
    //                         $error_counter++;
    //                         continue;
    //                     }
    //                     $class = Classes::where('name', $all_data[1])->where('owned_by', $enr->owned_by)->first();
    //                     if (!$class) {
    //                         $error_counter++;
    //                         continue;
    //                     }
    //                     $section = Section::where('name', $all_data[2])->first();
    //                     if (!$section) {
    //                         dd($section, $all_data, $enr, $reg, $class, 'section not found');
    //                         $error_counter++;
    //                         continue;
    //                     }
    //                     $sectionclass = ClassSection::where('class_id', $class->id)->where('section_id', $section->id)->where('owned_by', $enr->owned_by)->where('active_status', 1)->first();
    //                     if (!$sectionclass) {
    //                         if ($enr->owned_by == 53) {
    //                             $sectionclass = new ClassSection();
    //                             $sectionclass->class_id = $class->id;
    //                             $sectionclass->section_id = $section->id;
    //                             $sectionclass->owned_by = $enr->owned_by;
    //                             $sectionclass->created_by = auth()->user()->id;
    //                             $sectionclass->save();
    //                         } else {
    //                             dd($class->id, $section->id, $enr->owned_by);
    //                             $error_counter++;
    //                             continue;
    //                         }

    //                     }
    //                     if ($reg) {
    //                         $reg->mothername = $all_data[4];
    //                         $reg->fatherprofession = $all_data[5];
    //                         $reg->motherprofession = $all_data[6];
    //                         $reg->address = $all_data[7];
    //                         $reg->permanent_address = $all_data[7];
    //                         $reg->register_option = 1;
    //                         $reg->reg_class = $sectionclass->class_id;
    //                         $reg->class_id = $sectionclass->class_id;
    //                         $reg->save();

    //                     }
    //                     $enr->class_id = $sectionclass->class_id;
    //                     $enr->adm_session = 2;
    //                     $enr->section_id = $sectionclass->section_id;
    //                     $enr->save();
    //                     DB::commit();
    //                     $success_counter++;
    //                 }
    //                 $count++;
    //             }
    //             fclose($handle);
    //         }
    //         return redirect()->back()->with('success', "{$success_counter} Section added successfully. {$error_counter} rows skipped due to Duplication,incomplete or invalid data.");
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         dd($e);
    //         return redirect()->back()->with('error', "An error occurred: " . $e->getMessage());
    //     }
    // }
    // private function EnrollmentImport($file, $request)
    // {
    //     $request->validate([
    //         'excel_file' => 'required|file|mimes:csv,txt',
    //     ]);

    //     $file = $request->file('excel_file');
    //     $filename = $file->getClientOriginalName();
    //     $file->move(public_path('assets/import/csv_file/'), $filename);
    //     $filepath = public_path('assets/import/csv_file/' . $filename);

    //     // Initialize counters
    //     $success_counter = 0;
    //     $error_counter = 0;
    //     $duplication_counter = 0;
    //     $skipped_records = [];

    //     DB::beginTransaction();
    //     try {
    //         $filenames = $filename . "enrollments.csv";
    //         $new = fopen($filenames, 'w+');

    //         if (($handle = fopen($filepath, 'r')) !== FALSE) {
    //             $count = 0;

    //             while (($all_data = fgetcsv($handle, 4000, ",")) !== FALSE) {
    //                 // Skip header row or empty rows
    //                 if ($count == 0 || empty(array_filter($all_data))) {
    //                     $count++;
    //                     continue;
    //                 }

    //                 $all_data = array_map('trim', $all_data);
    //                 $registration = StudentRegistration::where('reg_no', $all_data[1])->first();

    //                 // Skip if registration doesn't exist
    //                 if ($registration === null) {
    //                     $skipped_records[] = [
    //                         'reg_no' => $all_data[1],
    //                         'reason' => 'Registration not found'
    //                     ];
    //                     $error_counter++;
    //                     $count++;
    //                     continue;
    //                 }

    //                 // Skip if student status is neither Registered nor empty
    //                 if (!in_array($registration->student_status, ['Registered', ''])) {
    //                     $skipped_records[] = [
    //                         'reg_no' => $all_data[1],
    //                         'reason' => 'Student status is not valid for enrollment'
    //                     ];
    //                     $error_counter++;
    //                     $count++;
    //                     continue;
    //                 }

    //                 // Check for existing enrollment to avoid duplication
    //                 $existingEnrollment = StudentEnrollments::where('regId', $all_data[1])
    //                     ->where('session_id', $registration->session_id)
    //                     ->first();

    //                 if ($existingEnrollment) {
    //                     $skipped_records[] = [
    //                         'reg_no' => $all_data[1],
    //                         'reason' => 'Student already enrolled in this session'
    //                     ];
    //                     $duplication_counter++;
    //                     $count++;
    //                     continue;
    //                 }

    //                 // Check for existing admission challan to avoid duplication
    //                 $existingChallan = Challans::where('student_id', $all_data[1])
    //                     ->where('session_id', $registration->session_id)
    //                     ->where('challan_type', 'Admission')
    //                     ->first();

    //                 if ($existingChallan) {
    //                     $skipped_records[] = [
    //                         'reg_no' => $all_data[1],
    //                         'reason' => 'Admission challan already exists for this student'
    //                     ];
    //                     $duplication_counter++;
    //                     $count++;
    //                     continue;
    //                 }

    //                 try {
    //                     // Get section once
    //                     $section = ClassSection::where('class_id', $registration->class_id)->first();

    //                     // Create enrollment
    //                     $newEnrollId = $all_data[11];
    //                     $created_at = date('Y-m-d H:i:s', strtotime($all_data[4]));

    //                     $enrollment = new StudentEnrollments();
    //                     $enrollment->enrollId = $newEnrollId;
    //                     $enrollment->regId = $all_data[1];
    //                     $enrollment->adm_date = date('Y-m-d', strtotime($all_data[4]));
    //                     $enrollment->class_id = $registration->class_id ?? NULL;
    //                     $enrollment->section_id = $section->id ?? NULL;
    //                     $enrollment->session_id = $registration->session_id;
    //                     $enrollment->owned_by = $registration->owned_by;
    //                     $enrollment->created_by = \Auth::user()->creatorId();
    //                     $enrollment->created_at = $created_at;
    //                     $enrollment->updated_at = $created_at;
    //                     $enrollment->save();

    //                     // Update registration status
    //                     $registration->roll_no = $newEnrollId;
    //                     $registration->student_status = 'Enrolled';
    //                     $registration->save();

    //                     // Process challan and fee heads
    //                     $this->processAdmissionChallan($all_data, $registration, $newEnrollId, $created_at);

    //                     $success_counter++;
    //                 } catch (\Exception $innerException) {
    //                     $skipped_records[] = [
    //                         'reg_no' => $all_data[1],
    //                         'reason' => 'Processing error: ' . $innerException->getMessage()
    //                     ];
    //                     $error_counter++;
    //                 }

    //                 $count++;
    //             }

    //             fclose($handle);
    //             if (is_resource($new)) {
    //                 fclose($new);
    //             }

    //             DB::commit();

    //             // Log results
    //             \Log::info("Enrollment Import Completed. Success: {$success_counter}, Duplicates: {$duplication_counter}, Errors: {$error_counter}");

    //             return [
    //                 'success' => true,
    //                 'message' => "Import completed. Processed: {$count}, Success: {$success_counter}, Duplicates: {$duplication_counter}, Errors: {$error_counter}",
    //                 'skipped_records' => $skipped_records
    //             ];
    //         }
    //     } catch (\Exception $e) {
    //         DB::rollback();
    //         if (is_resource($new)) {
    //             fclose($new);
    //         }
    //         dd($e);
    //         \Log::error("Enrollment Import Error: " . $e->getMessage());
    //         return [
    //             'success' => false,
    //             'message' => "Import failed: " . $e->getMessage()
    //         ];
    //     }
    // }



}


