@include('student.exports.header')
<table>
    <thead>
        <tr style="background: gray; font-size: 8px; font-family: <PERSON>ibri; font-weight: 800; border: 2px solid black; border-collapse: collapse;">
            <th style="width: 30px; font-size: 8px; font-family: <PERSON><PERSON>ri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">Sr</th>
            <th style="width: 30px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">Br.Sr</th>
            <th style="width: 50px; font-size: 8px; font-family: <PERSON><PERSON>ri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">Year</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">JAN</th>
            <th style="width: 40px; font-size: 8px; font-family: <PERSON><PERSON><PERSON>; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">FEB</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">MAR</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">APR</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">MAY</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">JUN</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">JUL</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">AUG</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">SEP</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">OCT</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">NOV</th>
            <th style="width: 40px; font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">DEC</th>
        </tr>
        {{-- branch --}}
        {{-- <tr>
            <td colspan="15" style="font-size: 8px; font-family: Calibri; font-weight: 600; border: 2px solid black; border-collapse: collapse; text-align: center; background:gray;">{{ $selectedBranchName }}</td>
        </tr> --}}
    </thead>
    <tbody>
        @php
            $sr = 1;
            $brSr = 1;
            $grouped = collect($data)->groupBy(function($item) {
                return \Carbon\Carbon::parse($item['month_year'])->format('Y');
            });
        @endphp
        @foreach ($grouped as $year => $months)
            <tr>
                <td style="text-align:center; border: 2px solid #D3D3D3; font-size: 8px; font-family: Calibri; background:rgb(163, 163, 163);">{{ $sr++ }}</td>
                <td style="text-align:center; border: 2px solid #D3D3D3; font-size: 8px; font-family: Calibri; background:rgb(163, 163, 163);">{{ $brSr++ }}</td>
                <td style="text-align:right; border: 2px solid #D3D3D3; font-size: 8px; font-family: Calibri; background:rgb(163, 163, 163);">{{ $year }}</td>
                @php
                    $monthTotals = array_fill(1, 12, 0);
                    foreach ($months as $m) {
                        $monthNum = \Carbon\Carbon::parse($m['month_year'])->month;
                        $monthTotals[$monthNum] +=
                            ($m['admissions'] ?? 0)
                            + ($m['withdrawals'] ?? 0)
                            + ($m['transfers_in'] ?? 0)
                            + ($m['transfers_out'] ?? 0)
                            + ($m['passing_out'] ?? 0);
                    }
                @endphp
                @for ($i = 1; $i <= 12; $i++)
                    <td style="text-align:right; border: 2px solid #D3D3D3; font-size: 8px; font-family: Calibri; background:rgb(163, 163, 163);">{{ $monthTotals[$i] > 0 ? $monthTotals[$i] : '' }}</td>
                @endfor
            </tr>
            {{-- <tr style="background: #e0e0e0; font-weight: bold;">
                <td colspan="3" style="text-align:right; border: 2px solid black; font-size: 8px; font-family: Calibri; font-weight: bold; background: #e0e0e0; text-align:center;">Total</td>
                @for ($i = 1; $i <= 12; $i++)
                    <td style="text-align:right; border: 2px solid black; font-size: 8px; font-family: Calibri; font-weight: bold; background: #e0e0e0;">{{ $monthTotals[$i] > 0 ? $monthTotals[$i] : '' }}</td>
                @endfor
            </tr> --}}
        @endforeach
    </tbody>
</table>
@include('student.exports.footer')