@extends('layouts.admin')

@section('page-title')
    {{ __('Student Single Account') }}
@endsection

@push('script-page')
    <script>
        function branchcustomer(id) {
            var customer = $('#customerselect').val();
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: "{{ route('branch.session_class') }}",
                type: "POST",
                data: {
                    id: id
                },
                dataType: 'json',
                success: function(result) {

                    if (result.status == 'success') {
                        var $classSelect = $('#class_select');
                        // Remove previous custom select wrapper and instance
                        if ($classSelect[0] && $classSelect[0].customSelectInstance) {
                            $classSelect[0].customSelectInstance.destroy();
                            delete $classSelect[0].customSelectInstance;
                        }
                        if ($classSelect.next('.custom-select-wrapper').length) {
                            $classSelect.next('.custom-select-wrapper').remove();
                        }
                        $classSelect.removeClass('custom-select');

                        // Clear and append new options
                        $classSelect.empty();
                        $classSelect.append($('<option>', {
                            value: 'all',
                            text: 'All Class'
                        }));
                        for (var j = 0; j < result.class.length; j++) {
                            var cls = result.class[j];
                            $classSelect.append($('<option>', {
                                value: cls.id,
                                text: cls.name
                            }));
                        }

                        // Re-add class and re-init
                        $classSelect.addClass('custom-select');
                        $classSelect.show();
                        // Directly create new CustomSelect instance for this select only
                        if (window.CustomSelect && typeof window.CustomSelect.create == 'function') {
                            window.CustomSelect.create($classSelect[0]);
                        }

                        // Session select update (unchanged)
                        $('#sessionselect').empty();
                        $('#sessionselect').append($('<option>', {
                            value: 'all',
                            text: 'All Session'
                        }));
                        for (var i = 0; i < result.session.length; i++) {
                            var session = result.session[i];
                            $('#sessionselect').append($('<option>', {
                                value: session.id,
                                text: session.title
                            }));
                        }
                    }
                    if (result.status == 'error') {}

                }
            });
        }

        function classStudents(classId, status) {
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: "{{ route('class.withdrawstudents') }}",
                type: "POST",
                data: {
                    class_id: classId,
                    status: status
                },
                dataType: 'json',
                success: function(result) {
                    console.log(result);
                    if (result.status == 'success') {
                        $('#student_select').empty();
                        $('#student_select').append($('<option>', {
                            value: 'all',
                            text: 'All Students'
                        }));
                        for (var id in result.students) {
                            if (result.students.hasOwnProperty(id)) {
                                $('#student_select').append($('<option>', {
                                    value: id,
                                    text: result.students[id]
                                }));
                            }
                        }
                        $('#student_select').val('all');
                    }
                }
            });
        }

        $(document).on('change', '#class_select, #status_select', function() {
            var classId = $('#class_select').val();
            var status = $('#status_select').val();
            if (classId && status) {
                classStudents(classId, status);
            }
        });

        $(document).on('change', '#class_select', function() {
            var classId = $(this).val();
            if (classId) {
                classStudents(classId);
            }
        });

        function validateForm() {
            var studentSelect = document.getElementById('student_select');

            if (studentSelect.value === '' || studentSelect.value === null) {
                alert('Please select a student before applying the filter.');
                return false;
            }
            document.getElementById('student_single_account').submit();
        }
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Student Single Account') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="mt-2 " id="multiCollapseExample1">
                <div class="card">
                    <div class="card-body filter_change">
                        {{ Form::open(['route' => ['student_single_account'], 'method' => 'GET', 'id' => 'student_single_account', 'novalidate' => 'novalidate']) }}
                        <div class="row d-flex justify-content-start ">
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                <div class="btn-box">
                                    {{ Form::label('from_date', __('From Date'), ['class' => 'form-label']) }}
                                    {{ Form::date('from_date', $from_date, ['class' => 'form-control']) }}
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                <div class="btn-box">
                                    {{ Form::label('to_date', __('To Date'), ['class' => 'form-label']) }}
                                    {{ Form::date('to_date', $to_date, ['class' => 'form-control']) }}
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                <div class="btn-box">
                                    {{ Form::label('branches', __('Branches'), ['class' => 'form-label']) }}
                                    {{ Form::select('branches', $branches, $selected_branch, ['class' => 'form-control select custom-select', 'onchange' => 'branchcustomer(this.value)']) }}
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                <div class="btn-box">
                                    {{ Form::label('status', __('Student Status'), ['class' => 'form-label']) }}
                                    {{ Form::select('status', ['active' => 'Active', 'withdraw' => 'Withdraw'], request()->get('status', 'active'), ['class' => 'form-control select', 'id' => 'status_select']) }}
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                <div class="btn-box">
                                    {{ Form::label('class', __('Class'), ['class' => 'form-label']) }}
                                    {{ Form::select('class', $class, $selected_class, ['class' => 'form-control select custom-select', 'id' => 'class_select', 'required' => 'required']) }}
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                <div class="btn-box">
                                    {{ Form::label('student', __('Students'), ['class' => 'form-label']) }}<span
                                        style="color: red"> *</span>
                                    {{ Form::select('student', $students, $selected_student, ['class' => 'form-control select custom-select', 'id' => 'student_select', 'required' => 'required']) }}
                                </div>
                            </div>
                            <div
                                class="col-xl-2 col-lg-2 col-md-6 col-sm-12 col-12 mr-2 mt-4 d-flex justify-content-end gap-2 align-items-center">
                                <!-- Search Button -->
                                <a href="#" class="btn mx-1 btn-sm btn-outline-primary"
                                    onclick="validateForm(); return false;" data-bs-title="{{ __('Apply') }}">
                                    <span class="btn-inner--icon">Search</span>
                                </a>
                                <a href="{{ route('student_single_account') }}" class="btn mx-1 btn-sm btn-outline-danger"
                                    data-bs-title="{{ __('Reset') }}">
                                    <span class="btn-inner--icon">Clear</span>
                                </a>
                                <!-- Actions Dropdown -->
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-success dropdown-toggle" type="button"
                                        id="actionDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        Export
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="actionDropdown">
                                        <li>
                                            <button class="dropdown-item" type="submit" name="export" value="excel">
                                                <i class="ti ti-file me-2"></i>Excel
                                            </button>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" type="submit" name="export" value="pdf">
                                                <i class="ti ti-download me-2"></i>Pdf
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        {{ Form::close() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="content" id="report-content">
        <div class="card p-4 table-responsive maximumHeightNew">
            <div style="width: 100%; text-align: center;">
                <p style="font-family:Edwardian Script ITC; font-size:3rem;"><b>The Lynx School</b></p>
            </div>
            <div style="width: 100%; text-align: center;">
                <p style="font-size:1rem; font-weight: 800;">
                <p style="text-align: center; font-weight: 900; font-size: 1rem;">Student Single Account</p>
                </p>
            </div>
            <div class="d-flex justify-content-between">
                <p><b>Period From :</b> {{ @$from_date }}</p>
                <p><b>Period To :</b> {{ @$to_date }}</p>
            </div>
            <div class="d-flex justify-content-between" style="flex-direction:column;">
                <div class="d-flex justify-content-between">
                    <p><b>Student Name: {{ @$std->stdname }}</b></p>
                    <p><b>Class: {{ @$std->class->name }}</b></p>
                    <p><b>Section: {{ @$std->enrollment->section->name }}</b></p>
                    <p><b>Roll No: {{ @$std->enrollment->enrollId }}</b></p>
                </div>
            </div>
            <table style="font-size:0.8rem;">
                <tr class="table_heads report_table">
                    <th>sr#</th>
                    <th>Student</th>
                    <th>Class</th>
                    <th>Billing Month</th>
                    <th>Challan No</th>
                    <th>Challan Type</th>
                    <th>Challan Amount</th>
                    <th>Payment Date</th>
                    <th>Late Amount</th>
                    <th>Receipt</th>
                    <th>Arrears</th>
                    <th>Receipt Mode</th>
                    <th>T.HEAD/Product</th>
                    <th>T.Amount</th>
                    <th>Receipt Ref.</th>
                </tr>
                <tbody>
                    @php
                        $headtotal = 0;
                        $dailyTotal = 0;
                        $currentDate = null;
                    @endphp
                    @foreach (@$receipts as $receipt)
                        <tr>
                            <td>{{ @$loop->iteration }}</td>
                            <td>{{ @$receipt->user->stdname }}</td>
                            @if ($receipt->journalEntery->category == 'Studypack')
                                <td>{{ @$receipt->journalEntery->studypackchallan->class->name }}</td>
                                <td>{{ @$receipt->journalEntery->studypackchallan->fee_month }}</td>
                                <td>{{ @$receipt->journalEntery->studypackchallan->challanNo }}</td>
                                <td>{{ @$receipt->journalEntery->studypackchallan->challan_type }}</td>
                                <td>{{ @$receipt->journalEntery->studypackchallan->total_amount }}</td>
                                <td>{{ @$receipt->journalEntery->stdRecp->recipt_date }}</td>
                                <td>{{ @$receipt->journalEntery->stdRecp->late_amount }}</td>
                                <td>{{ @$receipt->journalEntery->stdRecp->recipt_amount }}</td>
                                <td>{{ @$receipt->journalEntery->stdRecp->arrears }}</td>
                                <td>{{ @$receipt->journalEntery->stdRecp->receive_type }}</td>
                                <td>-</td>
                            @else
                                <td>{{ @$receipt->journalEntery->challan->class->name }}</td>
                                <td>{{ @$receipt->journalEntery->challan->fee_month }}</td>
                                <td>{{ @$receipt->journalEntery->challan->challanNo }}</td>
                                <td>{{ @$receipt->journalEntery->challan->challan_type }}</td>
                                <td>{{ @$receipt->journalEntery->challan->total_amount }}</td>
                                <td>{{ @$receipt->journalEntery->recipt->recipt_date }}</td>
                                <td>{{ @$receipt->journalEntery->recipt->late_amount }}</td>
                                <td>{{ @$receipt->journalEntery->recipt->recipt_amount }}</td>
                                <td>{{ @$receipt->journalEntery->recipt->arrears }}</td>
                                <td>{{ @$receipt->journalEntery->recipt->receive_type }}</td>
                                @php
                                    $headname = \App\Models\FeeHead::where('id', $receipt->head)->first();
                                @endphp
                                <td>{{ @$headname->fee_head }}</td>
                            @endif
                            {{-- @dd($receipt->studypackchallan->items); --}}
                            {{-- <td>{{ @$receipt->studypackchallan->items->product->name }}</td> --}}
                            <td>{{ @$receipt->credit }}</td>
                            <td>{{ @$receipt->journalEntery->recipt->referance }}</td>
                        </tr>
                    @endforeach



                    {{-- @foreach (@$receipts as $receipt)
                        @php
                            // Check if we are processing a new date
                            $receiptDate = \Carbon\Carbon::parse(@$receipt->recipt_date)->format('d-M-Y');
                            if ($currentDate !== $receiptDate && $currentDate !== null) {
                                // Display the total for the previous day
                                echo '<tr>
                            <td colspan="13" class="text-right"><strong>Total for ' .
                                    $currentDate .
                                    ':</strong></td>
                            <td><strong>' .
                                    $dailyTotal .
                                    '</strong></td>
                            <td></td>
                        </tr>';
                                // Reset daily total for the new date
                                $dailyTotal = 0;
                            }
                            $currentDate = $receiptDate;
                        @endphp

                        @foreach (@$receipt->voucher as $voucher)
                            <tr>
                                @php
                                    $headtotal += $voucher->credit;
                                    $dailyTotal += $voucher->credit;
                                @endphp
                                <td>{{ @$loop->parent->iteration }}</td>
                                <td>{{ @$receipt->challan->class->name }}</td>
                                <td>{{ @$receipt->challan->student->stdname }}</td>
                                <td>{{ \Carbon\Carbon::parse(@$receipt->challan->fee_month)->format('M-y') }}</td>
                                <td>{{ @$receipt->challan->challanNo }}</td>
                                <td>{{ @$receipt->challan->challan_type }}</td>
                                <td>{{ @$receipt->challan_amount }}</td>
                                <td>{{ \Carbon\Carbon::parse(@$receipt->recipt_date)->format('d-M-Y') }}</td>
                                <td>{{ @$receipt->late_amount }}</td>
                                <td>{{ @$receipt->recipt_amount }}</td>
                                <td>{{ @$receipt->arrears }}</td>
                                <td>{{ @$receipt->receive_type }}</td>
                                <td>{{ @$voucher->heads->fee_head }}</td>
                                <td>{{ @$voucher->credit }}</td>
                                <td>{{ @$receipt->referance }}</td>
                            </tr>
                        @endforeach
                    @endforeach --}}

                    @if ($currentDate !== null)
                        <tr>
                            <td colspan="13" class="text-right"><strong>Total for {{ $currentDate }}:</strong></td>
                            <td><strong>{{ $dailyTotal }}</strong></td>
                            <td></td>
                        </tr>
                    @endif
                    @if ($headtotal != 0)
                        <tr style="background-color:grey; color:white;">
                            <td colspan="11" class="text-right"></td>
                            <td colspan="2"><strong>Total Amount</strong></td>
                            <td>{{ $dailyTotal }}</td>
                            <td></td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
@endsection
