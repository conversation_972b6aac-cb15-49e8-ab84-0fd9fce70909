@include('student.exports.header')

<table style="width: 100%; font-size: 8px; font-family: <PERSON><PERSON>ri; border-collapse: collapse;">
    <thead>
        <tr style="background-color: gray;">
            <th
                style="border: 1px solid black; text-align: center; font-weight: bold; background-color: gray; font-size: 8px; padding: 4px; width: 15%;">
                Class</th>
            @foreach ($heads as $head)
                <th
                    style="border: 1px solid black; text-align: center; font-weight: bold; background-color: gray; font-size: 8px; padding: 4px; width: 5%;">
                    {{ $head->fee_head }}</th>
            @endforeach
            <th
                style="border: 1px solid black; text-align: center; font-weight: bold; background-color: gray; font-size: 8px; padding: 4px; width: 8%;">
                Total</th>
        </tr>
    </thead>
    <tbody> 
        @foreach ($classes as $class)
            <tr>
                <td
                    style="padding: 4px; text-align: left; font-weight: bold; width: 80px; font-size: 8px;">
                    {{ $class->name }}</td>
                @php $total = 0; @endphp
                @foreach ($heads as $head)
                    @php
                        $amount = optional($class->classhead->firstWhere('head_id', $head->id))->amount;
                        $total += $amount ?? 0;
                    @endphp
                    <td style="padding: 4px; text-align: right; width: 80px; font-size: 8px;">
                        {{ $amount ? number_format($amount) : '' }}
                    </td>
                @endforeach
                <td style="padding: 4px; text-align: right; width: 60px; font-size: 8px;">
                    {{ $total ? number_format($total) : '' }}
                </td>
            </tr>
        @endforeach
    </tbody>
</table>

@include('student.exports.footer')